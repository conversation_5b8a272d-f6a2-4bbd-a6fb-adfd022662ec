#!/usr/bin/env python3
"""
测试导入是否正确
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块的导入"""
    try:
        print("测试导入...")
        
        # 测试基础模块
        from models.user import db, User
        print("✅ models.user 导入成功")
        
        from models.assessment import (TechSkill, SoftSkill, Project, 
                                     LearningActivity, IndustryKnowledge, 
                                     CareerGoal, EthicsCheck, HealthMetric)
        print("✅ models.assessment 导入成功")
        
        from models.auth import auth_bp, token_required
        print("✅ models.auth 导入成功")
        
        from models.profession_templates import PROFESSION_TEMPLATES
        print("✅ models.profession_templates 导入成功")
        
        from api.assessment_routes import assessment_bp
        print("✅ api.assessment_routes 导入成功")
        
        from api.profession_routes import profession_bp
        print("✅ api.profession_routes 导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
