from flask import Flask, jsonify
from flask_cors import CORS
import os

def create_app():
    app = Flask(__name__)
    CORS(app)  # 启用CORS支持跨域请求

    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ai_dev_dashboard.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev_key')

    # 初始化数据库
    from models.user import db
    db.init_app(app)

    # 注册蓝图
    from models.auth import auth_bp
    from models.skills import skills_bp
    from api.assessment_routes import assessment_bp
    from api.profession_routes import profession_bp  # 新增职业管理路由

    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(skills_bp, url_prefix='/api/skills')
    app.register_blueprint(assessment_bp, url_prefix='/api/assessment')
    app.register_blueprint(profession_bp, url_prefix='/api/profession')  # 新增

    # 创建数据库表
    with app.app_context():
        # 导入所有模型以确保表被创建
        from models.assessment import (TechSkill, SoftSkill, Project, LearningActivity,
                                     IndustryKnowledge, CareerGoal, EthicsCheck, HealthMetric,
                                     ProfessionTemplate, SkillCategory)
        db.create_all()

    @app.route('/')
    def index():
        return jsonify({'message': '通用职业能力管理平台API服务'})

    @app.route('/api')
    def api_index():
        return jsonify({'message': '通用职业能力管理平台API服务', 'version': '2.0'})

    @app.route('/test_universal_features.html')
    def test_page():
        """提供测试页面"""
        with open('test_universal_features.html', 'r', encoding='utf-8') as f:
            return f.read(), 200, {'Content-Type': 'text/html; charset=utf-8'}

    @app.route('/frontend_integration_test.html')
    def frontend_test_page():
        """提供前端集成测试页面"""
        with open('frontend_integration_test.html', 'r', encoding='utf-8') as f:
            return f.read(), 200, {'Content-Type': 'text/html; charset=utf-8'}

    return app

app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
