from flask import Blueprint, jsonify, request
from models.user import db, User
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime
import os
from functools import wraps

auth_bp = Blueprint('auth', __name__)

# JWT令牌验证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '未提供令牌'}), 401

        try:
            # 移除Bearer前缀
            token = token.split(' ')[1] if token.startswith('Bearer ') else token
            payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
            current_user_id = payload['user_id']

            # 验证用户是否存在
            user = User.query.get(current_user_id)
            if not user:
                return jsonify({'message': '用户不存在'}), 404

        except jwt.ExpiredSignatureError:
            return jsonify({'message': '令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': '无效的令牌'}), 401
        except IndexError:
            return jsonify({'message': '令牌格式错误'}), 401

        return f(current_user_id, *args, **kwargs)
    return decorated

# 用户注册
@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    # 检查用户是否已存在
    if User.query.filter_by(username=data['username']).first() or User.query.filter_by(email=data['email']).first():
        return jsonify({'message': '用户名或邮箱已存在'}), 400

    # 创建新用户
    hashed_password = generate_password_hash(data['password'])  # 使用默认的scrypt方法
    new_user = User(
        username=data['username'],
        email=data['email'],
        password_hash=hashed_password
    )

    db.session.add(new_user)
    db.session.commit()

    return jsonify({'message': '用户注册成功'}), 201

# 用户登录
@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    user = User.query.filter_by(username=data['username']).first()

    if not user or not check_password_hash(user.password_hash, data['password']):
        return jsonify({'message': '用户名或密码错误'}), 401

    # 生成JWT令牌
    token = jwt.encode({
        'user_id': user.id,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=1)
    }, os.environ.get('SECRET_KEY', 'dev_key'), algorithm='HS256')

    return jsonify({
        'message': '登录成功',
        'token': token,
        'user_id': user.id,
        'username': user.username
    }), 200

# 获取用户信息
@auth_bp.route('/user', methods=['GET'])
def get_user():
    # 从请求头获取令牌
    token = request.headers.get('Authorization')
    if not token:
        return jsonify({'message': '未提供令牌'}), 401

    try:
        # 解码令牌
        token = token.split(' ')[1]  # 移除Bearer前缀
        payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
        user_id = payload['user_id']

        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404

        return jsonify({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'created_at': user.created_at
        }), 200

    except jwt.ExpiredSignatureError:
        return jsonify({'message': '令牌已过期'}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': '无效的令牌'}), 401
