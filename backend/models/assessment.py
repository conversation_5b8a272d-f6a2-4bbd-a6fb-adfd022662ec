from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from user import db

# 新增：职业模板配置模型
class ProfessionTemplate(db.Model):
    """职业模板配置"""
    __tablename__ = 'profession_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 职业名称
    display_name = db.Column(db.String(100), nullable=False)  # 显示名称
    description = db.Column(db.Text)  # 职业描述
    config = db.Column(db.JSON)  # 职业配置（技能分类、评估维度等）
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<ProfessionTemplate {self.name}>'

# 新增：技能分类配置模型
class SkillCategory(db.Model):
    """技能分类配置"""
    __tablename__ = 'skill_categories'

    id = db.Column(db.Integer, primary_key=True)
    profession_template_id = db.Column(db.Integer, db.ForeignKey('profession_templates.id'), nullable=False)
    category_type = db.Column(db.String(20), nullable=False)  # 'tech' 或 'soft'
    name = db.Column(db.String(100), nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f'<SkillCategory {self.name}>'

class TechSkill(db.Model):
    """技术能力评估模型 - 通用化改造"""
    __tablename__ = 'tech_skills'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    category = db.Column(db.String(50), nullable=False)  # 技能分类
    name = db.Column(db.String(100), nullable=False)  # 具体技能名称
    score = db.Column(db.Float, nullable=False)  # 1-5分评分
    evidence = db.Column(db.Text)  # 证据案例
    priority = db.Column(db.String(1), nullable=False)  # 优先级：H/M/L
    profession_type = db.Column(db.String(50), default='ai_engineer')  # 职业类型
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<TechSkill {self.name}>'

class SoftSkill(db.Model):
    """软技能评估模型"""
    __tablename__ = 'soft_skills'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)  # 如：沟通表达、团队协作等
    score = db.Column(db.Float, nullable=False)  # 1-5分评分
    evidence = db.Column(db.Text)  # 案例说明
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<SoftSkill {self.name}>'

class Project(db.Model):
    """项目经验复盘模型"""
    __tablename__ = 'projects'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    objective = db.Column(db.Text)  # 项目目标
    tech_challenge = db.Column(db.Text)  # 技术难点
    solution = db.Column(db.Text)  # 解决方案
    outcome = db.Column(db.Text)  # 成果量化
    reflection = db.Column(db.Text)  # 不足反思
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Project {self.title}>'

class LearningActivity(db.Model):
    """学习能力诊断模型"""
    __tablename__ = 'learning_activities'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    activity_type = db.Column(db.String(50), nullable=False)  # 课程/书籍/论文等
    title = db.Column(db.String(200), nullable=False)
    hours_spent = db.Column(db.Float)  # 投入时间
    knowledge_repo_link = db.Column(db.String(255))  # 知识库链接
    completion_status = db.Column(db.String(20))  # 完成状态
    notes = db.Column(db.Text)
    date = db.Column(db.Date, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<LearningActivity {self.title}>'

class IndustryKnowledge(db.Model):
    """行业知识匹配度分析模型"""
    __tablename__ = 'industry_knowledge'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    industry = db.Column(db.String(100), nullable=False)  # 目标行业
    dimension = db.Column(db.String(100), nullable=False)  # 领域知识/业务场景/合规要求
    question = db.Column(db.Text, nullable=False)  # 自检问题
    status = db.Column(db.Text)  # 现状描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<IndustryKnowledge {self.industry}:{self.dimension}>'

class CareerGoal(db.Model):
    """职业发展对标模型"""
    __tablename__ = 'career_goals'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    goal_type = db.Column(db.String(20), nullable=False)  # 短期/长期
    description = db.Column(db.Text)
    progress = db.Column(db.Float, default=0)  # 进度百分比
    resource_gap = db.Column(db.Text)  # 资源缺口
    target_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<CareerGoal {self.title}>'

class EthicsCheck(db.Model):
    """伦理与合规自检模型"""
    __tablename__ = 'ethics_checks'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    check_item = db.Column(db.String(200), nullable=False)
    status = db.Column(db.Boolean, default=False)  # 是/否
    practice_case = db.Column(db.Text)  # 具体实践案例
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<EthicsCheck {self.check_item}>'

class HealthMetric(db.Model):
    """健康与动力评估模型"""
    __tablename__ = 'health_metrics'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    metric_type = db.Column(db.String(50), nullable=False)  # 工作节奏/学习倦怠/社交充电/身体信号
    value = db.Column(db.Float)  # 数值评估
    description = db.Column(db.Text)  # 描述
    date = db.Column(db.Date, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<HealthMetric {self.metric_type}>'
