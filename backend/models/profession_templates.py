#!/usr/bin/env python3
"""
职业模板配置管理
支持多种职业类型的能力评估框架
"""

# 职业模板配置
PROFESSION_TEMPLATES = {
    "ai_engineer": {
        "name": "ai_engineer",
        "display_name": "AI应用工程师",
        "description": "专注于AI应用开发、模型部署和智能系统构建的工程师",
        "config": {
            "tech_skill_categories": [
                {"name": "programming", "display_name": "编程语言", "description": "核心编程技能"},
                {"name": "frameworks", "display_name": "框架工具", "description": "AI/ML框架和开发工具"},
                {"name": "data_processing", "display_name": "数据处理", "description": "数据处理和分析能力"},
                {"name": "model_skills", "display_name": "模型能力", "description": "AI模型相关技能"},
                {"name": "engineering", "display_name": "工程化", "description": "系统工程和部署能力"}
            ],
            "soft_skills": [
                "沟通表达", "团队协作", "问题解决", "学习能力", 
                "项目管理", "创新思维", "压力应对", "批判性思维"
            ],
            "industries": [
                "金融科技", "医疗健康", "教育科技", "智能制造", 
                "电商零售", "自动驾驶", "智慧城市", "游戏娱乐"
            ],
            "ethics_checks": [
                "数据隐私保护", "算法公平性", "透明度原则", "责任归属",
                "安全可靠", "用户同意", "可解释性", "持续监控"
            ]
        }
    },
    
    "frontend_developer": {
        "name": "frontend_developer", 
        "display_name": "前端开发工程师",
        "description": "专注于用户界面开发和用户体验优化的工程师",
        "config": {
            "tech_skill_categories": [
                {"name": "languages", "display_name": "编程语言", "description": "前端开发语言"},
                {"name": "frameworks", "display_name": "框架库", "description": "前端框架和库"},
                {"name": "tools", "display_name": "开发工具", "description": "构建工具和开发环境"},
                {"name": "design", "display_name": "设计能力", "description": "UI/UX设计相关"},
                {"name": "performance", "display_name": "性能优化", "description": "前端性能优化技能"}
            ],
            "soft_skills": [
                "沟通表达", "团队协作", "用户思维", "学习能力",
                "细节把控", "创新思维", "时间管理", "问题解决"
            ],
            "industries": [
                "互联网", "电商", "金融", "教育", "医疗", "游戏", "企业服务", "移动应用"
            ],
            "ethics_checks": [
                "用户隐私保护", "无障碍设计", "性能优化", "安全防护",
                "用户体验", "数据安全", "跨平台兼容", "代码质量"
            ]
        }
    },
    
    "backend_developer": {
        "name": "backend_developer",
        "display_name": "后端开发工程师", 
        "description": "专注于服务器端开发和系统架构设计的工程师",
        "config": {
            "tech_skill_categories": [
                {"name": "languages", "display_name": "编程语言", "description": "后端开发语言"},
                {"name": "frameworks", "display_name": "框架技术", "description": "后端框架和技术栈"},
                {"name": "databases", "display_name": "数据库", "description": "数据库设计和管理"},
                {"name": "architecture", "display_name": "系统架构", "description": "系统设计和架构能力"},
                {"name": "devops", "display_name": "运维部署", "description": "部署和运维相关技能"}
            ],
            "soft_skills": [
                "逻辑思维", "团队协作", "问题解决", "学习能力",
                "系统思维", "沟通表达", "压力应对", "技术领导"
            ],
            "industries": [
                "互联网", "金融", "电商", "游戏", "企业服务", "云计算", "物联网", "区块链"
            ],
            "ethics_checks": [
                "数据安全", "系统稳定性", "性能优化", "代码质量",
                "安全防护", "隐私保护", "可扩展性", "监控告警"
            ]
        }
    },
    
    "product_manager": {
        "name": "product_manager",
        "display_name": "产品经理",
        "description": "负责产品规划、设计和管理的专业人员",
        "config": {
            "tech_skill_categories": [
                {"name": "analysis", "display_name": "数据分析", "description": "数据分析和洞察能力"},
                {"name": "design", "display_name": "产品设计", "description": "产品设计和原型制作"},
                {"name": "research", "display_name": "用户研究", "description": "用户调研和需求分析"},
                {"name": "tools", "display_name": "工具使用", "description": "产品管理工具和平台"},
                {"name": "technical", "display_name": "技术理解", "description": "技术基础和理解能力"}
            ],
            "soft_skills": [
                "沟通表达", "逻辑思维", "用户思维", "学习能力",
                "项目管理", "决策能力", "创新思维", "团队协作"
            ],
            "industries": [
                "互联网", "移动应用", "SaaS", "电商", "金融", "教育", "医疗", "游戏"
            ],
            "ethics_checks": [
                "用户隐私", "产品安全", "用户体验", "数据使用",
                "功能合规", "内容审核", "用户权益", "社会责任"
            ]
        }
    },
    
    "data_scientist": {
        "name": "data_scientist",
        "display_name": "数据科学家",
        "description": "专注于数据分析、机器学习和商业洞察的专业人员",
        "config": {
            "tech_skill_categories": [
                {"name": "programming", "display_name": "编程语言", "description": "数据科学编程技能"},
                {"name": "statistics", "display_name": "统计分析", "description": "统计学和数学基础"},
                {"name": "ml_algorithms", "display_name": "机器学习", "description": "机器学习算法和模型"},
                {"name": "data_tools", "display_name": "数据工具", "description": "数据处理和可视化工具"},
                {"name": "business", "display_name": "业务理解", "description": "业务分析和洞察能力"}
            ],
            "soft_skills": [
                "逻辑思维", "问题解决", "沟通表达", "学习能力",
                "批判性思维", "好奇心", "团队协作", "商业敏感"
            ],
            "industries": [
                "金融", "电商", "医疗", "制造", "咨询", "互联网", "保险", "零售"
            ],
            "ethics_checks": [
                "数据隐私", "算法公平", "模型透明", "数据质量",
                "偏见检测", "结果解释", "数据安全", "合规使用"
            ]
        }
    },
    
    "ui_ux_designer": {
        "name": "ui_ux_designer",
        "display_name": "UI/UX设计师",
        "description": "专注于用户界面和用户体验设计的专业人员",
        "config": {
            "tech_skill_categories": [
                {"name": "design_tools", "display_name": "设计工具", "description": "专业设计软件和工具"},
                {"name": "prototyping", "display_name": "原型制作", "description": "交互原型和设计验证"},
                {"name": "user_research", "display_name": "用户研究", "description": "用户调研和测试方法"},
                {"name": "visual_design", "display_name": "视觉设计", "description": "视觉设计和品牌表达"},
                {"name": "frontend_basics", "display_name": "前端基础", "description": "基础前端技术理解"}
            ],
            "soft_skills": [
                "创意思维", "用户思维", "沟通表达", "学习能力",
                "细节把控", "团队协作", "审美能力", "同理心"
            ],
            "industries": [
                "互联网", "移动应用", "游戏", "电商", "金融", "教育", "医疗", "品牌设计"
            ],
            "ethics_checks": [
                "无障碍设计", "用户隐私", "包容性设计", "用户体验",
                "视觉舒适", "信息准确", "文化敏感", "设计伦理"
            ]
        }
    }
}

def get_profession_template(profession_name):
    """获取指定职业的模板配置"""
    return PROFESSION_TEMPLATES.get(profession_name)

def get_all_professions():
    """获取所有支持的职业类型"""
    return list(PROFESSION_TEMPLATES.keys())

def get_profession_display_names():
    """获取所有职业的显示名称"""
    return {k: v["display_name"] for k, v in PROFESSION_TEMPLATES.items()}
