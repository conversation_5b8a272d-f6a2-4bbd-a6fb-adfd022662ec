from flask import Blueprint, jsonify, request
from user import db
from assessment import TechSkill, SoftSkill
import jwt
import os
from functools import wraps

# 创建蓝图
skills_bp = Blueprint('skills', __name__)

# 验证令牌的装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '未提供令牌'}), 401
        
        try:
            token = token.split(' ')[1]
            payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
            current_user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({'message': '令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': '无效的令牌'}), 401
        
        return f(current_user_id, *args, **kwargs)
    
    return decorated

# 获取用户所有技术技能
@skills_bp.route('/tech-skills', methods=['GET'])
@token_required
def get_tech_skills(current_user_id):
    skills = TechSkill.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': skill.id,
        'category': skill.category,
        'name': skill.name,
        'score': skill.score,
        'evidence': skill.evidence,
        'priority': skill.priority,
        'created_at': skill.created_at,
        'updated_at': skill.updated_at
    } for skill in skills])

# 添加新技术技能
@skills_bp.route('/tech-skills', methods=['POST'])
@token_required
def add_tech_skill(current_user_id):
    data = request.get_json()
    
    new_skill = TechSkill(
        user_id=current_user_id,
        category=data['category'],
        name=data['name'],
        score=data['score'],
        evidence=data.get('evidence', ''),
        priority=data.get('priority', 'M')
    )
    
    db.session.add(new_skill)
    db.session.commit()
    
    return jsonify({'message': '技术技能添加成功'}), 201

# 更新技术技能
@skills_bp.route('/tech-skills/<int:skill_id>', methods=['PUT'])
@token_required
def update_tech_skill(current_user_id, skill_id):
    skill = TechSkill.query.filter_by(id=skill_id, user_id=current_user_id).first()
    if not skill:
        return jsonify({'message': '技能不存在'}), 404
    
    data = request.get_json()
    skill.category = data.get('category', skill.category)
    skill.name = data.get('name', skill.name)
    skill.score = data.get('score', skill.score)
    skill.evidence = data.get('evidence', skill.evidence)
    skill.priority = data.get('priority', skill.priority)
    
    db.session.commit()
    
    return jsonify({'message': '技术技能更新成功'})

# 删除技术技能
@skills_bp.route('/tech-skills/<int:skill_id>', methods=['DELETE'])
@token_required
def delete_tech_skill(current_user_id, skill_id):
    skill = TechSkill.query.filter_by(id=skill_id, user_id=current_user_id).first()
    if not skill:
        return jsonify({'message': '技能不存在'}), 404
    
    db.session.delete(skill)
    db.session.commit()
    
    return jsonify({'message': '技术技能删除成功'})

# 获取用户所有软技能
@skills_bp.route('/soft-skills', methods=['GET'])
@token_required
def get_soft_skills(current_user_id):
    skills = SoftSkill.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': skill.id,
        'name': skill.name,
        'score': skill.score,
        'evidence': skill.evidence,
        'created_at': skill.created_at,
        'updated_at': skill.updated_at
    } for skill in skills])

# 添加新软技能
@skills_bp.route('/soft-skills', methods=['POST'])
@token_required
def add_soft_skill(current_user_id):
    data = request.get_json()
    
    new_skill = SoftSkill(
        user_id=current_user_id,
        name=data['name'],
        score=data['score'],
        evidence=data.get('evidence', '')
    )
    
    db.session.add(new_skill)
    db.session.commit()
    
    return jsonify({'message': '软技能添加成功'}), 201

# 更新软技能
@skills_bp.route('/soft-skills/<int:skill_id>', methods=['PUT'])
@token_required
def update_soft_skill(current_user_id, skill_id):
    skill = SoftSkill.query.filter_by(id=skill_id, user_id=current_user_id).first()
    if not skill:
        return jsonify({'message': '技能不存在'}), 404
    
    data = request.get_json()
    skill.name = data.get('name', skill.name)
    skill.score = data.get('score', skill.score)
    skill.evidence = data.get('evidence', skill.evidence)
    
    db.session.commit()
    
    return jsonify({'message': '软技能更新成功'})

# 删除软技能
@skills_bp.route('/soft-skills/<int:skill_id>', methods=['DELETE'])
@token_required
def delete_soft_skill(current_user_id, skill_id):
    skill = SoftSkill.query.filter_by(id=skill_id, user_id=current_user_id).first()
    if not skill:
        return jsonify({'message': '技能不存在'}), 404
    
    db.session.delete(skill)
    db.session.commit()
    
    return jsonify({'message': '软技能删除成功'})
