#!/bin/bash

# SkillForge 项目启动脚本
# 用于快速启动前端和后端服务

echo "🚀 启动 SkillForge 项目..."

# 检查是否安装了必要的依赖
check_dependencies() {
    echo "📋 检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.7+"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请先安装 npm"
        exit 1
    fi
    
    echo "✅ 依赖检查完成"
}

# 安装后端依赖
install_backend_deps() {
    echo "📦 安装后端依赖..."
    cd backend
    
    if [ ! -f "requirements.txt" ]; then
        echo "❌ 未找到 requirements.txt 文件"
        exit 1
    fi
    
    pip3 install -r requirements.txt
    cd ..
    echo "✅ 后端依赖安装完成"
}

# 安装前端依赖
install_frontend_deps() {
    echo "📦 安装前端依赖..."
    cd frontend
    
    if [ ! -f "package.json" ]; then
        echo "❌ 未找到 package.json 文件"
        exit 1
    fi
    
    npm install
    cd ..
    echo "✅ 前端依赖安装完成"
}

# 启动后端服务
start_backend() {
    echo "🔧 启动后端服务..."
    cd backend
    python3 main.py &
    BACKEND_PID=$!
    cd ..
    echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"
    echo "🌐 后端服务地址: http://localhost:5002"
}

# 启动前端服务
start_frontend() {
    echo "🎨 启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"
    echo "🌐 前端服务地址: http://localhost:5173"
}

# 主函数
main() {
    echo "======================================"
    echo "🎯 SkillForge 全方位职业能力管理平台"
    echo "======================================"
    
    # 检查依赖
    check_dependencies
    
    # 询问是否需要安装依赖
    read -p "是否需要安装/更新依赖? (y/n): " install_deps
    if [[ $install_deps == "y" || $install_deps == "Y" ]]; then
        install_backend_deps
        install_frontend_deps
    fi
    
    # 启动服务
    echo ""
    echo "🚀 启动服务..."
    start_backend
    sleep 3  # 等待后端启动
    start_frontend
    
    echo ""
    echo "======================================"
    echo "🎉 SkillForge 启动完成！"
    echo "======================================"
    echo "📱 前端地址: http://localhost:5173"
    echo "🔧 后端地址: http://localhost:5002"
    echo ""
    echo "💡 提示:"
    echo "  - 按 Ctrl+C 停止服务"
    echo "  - 或运行 ./stop.sh 停止服务"
    echo "======================================"
    
    # 保存PID到文件
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
    
    # 等待用户中断
    wait
}

# 运行主函数
main
