#!/bin/bash

# 通用职业能力管理平台 - 停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🛑 停止通用职业能力管理平台..." $BLUE

# 从PID文件读取进程ID并停止
if [ -f "../../.backend.pid" ]; then
    BACKEND_PID=$(cat ../../.backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_message "停止后端服务 (PID: $BACKEND_PID)..." $YELLOW
        kill $BACKEND_PID
        print_message "✅ 后端服务已停止" $GREEN
    else
        print_message "后端服务已经停止" $YELLOW
    fi
    rm -f ../../.backend.pid
fi

if [ -f "../../.frontend.pid" ]; then
    FRONTEND_PID=$(cat ../../.frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_message "停止前端服务 (PID: $FRONTEND_PID)..." $YELLOW
        kill $FRONTEND_PID
        print_message "✅ 前端服务已停止" $GREEN
    else
        print_message "前端服务已经停止" $YELLOW
    fi
    rm -f ../../.frontend.pid
fi

# 强制停止可能残留的进程
print_message "🔍 检查残留进程..." $BLUE

# 停止可能的Flask进程
FLASK_PIDS=$(pgrep -f "python.*main.py" 2>/dev/null || true)
if [ ! -z "$FLASK_PIDS" ]; then
    print_message "发现Flask进程，正在停止..." $YELLOW
    echo $FLASK_PIDS | xargs kill 2>/dev/null || true
fi

# 停止可能的Vite进程
VITE_PIDS=$(pgrep -f "vite" 2>/dev/null || true)
if [ ! -z "$VITE_PIDS" ]; then
    print_message "发现Vite进程，正在停止..." $YELLOW
    echo $VITE_PIDS | xargs kill 2>/dev/null || true
fi

# 停止可能占用端口的进程
PORT_5002_PID=$(lsof -ti:5002 2>/dev/null || true)
if [ ! -z "$PORT_5002_PID" ]; then
    print_message "停止占用端口5002的进程..." $YELLOW
    kill $PORT_5002_PID 2>/dev/null || true
fi

PORT_5175_PID=$(lsof -ti:5175 2>/dev/null || true)
if [ ! -z "$PORT_5175_PID" ]; then
    print_message "停止占用端口5175的进程..." $YELLOW
    kill $PORT_5175_PID 2>/dev/null || true
fi

print_message "✅ 所有服务已停止" $GREEN

# 显示清理信息
print_message "🧹 清理完成:" $BLUE
print_message "   • 后端服务已停止" $NC
print_message "   • 前端服务已停止" $NC
print_message "   • PID文件已清理" $NC
print_message "   • 端口已释放" $NC

print_message "💡 提示: 使用 ./start.sh 重新启动服务" $BLUE
