#!/usr/bin/env python3
"""
修复前端组件导入路径的脚本
"""

import os
import re

def fix_imports_in_file(file_path):
    """修复单个文件中的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复API导入路径
        content = re.sub(r"from ['\"]\.\/src\/api['\"]", "from '../api'", content)
        content = re.sub(r"from ['\"]\.\/api['\"]", "from '../api'", content)
        
        # 修复组件导入路径（在pages目录中）
        if '/pages/' in file_path:
            # 修复从当前目录导入组件的路径
            content = re.sub(r"from ['\"]\.\/([A-Z][a-zA-Z]*)['\"]", r"from '../components/\1'", content)
            
        # 修复组件导入路径（在components目录中）
        if '/components/' in file_path:
            # 修复从当前目录导入页面的路径
            content = re.sub(r"from ['\"]\.\.\/([A-Z][a-zA-Z]*Page)['\"]", r"from '../pages/\1'", content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⚪ {file_path} 无需修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复前端组件导入路径...")
    
    # 需要检查的目录
    directories = [
        'frontend/src/components',
        'frontend/src/pages'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n📁 检查目录: {directory}")
            
            for filename in os.listdir(directory):
                if filename.endswith('.tsx') or filename.endswith('.ts'):
                    file_path = os.path.join(directory, filename)
                    total_count += 1
                    
                    if fix_imports_in_file(file_path):
                        fixed_count += 1
        else:
            print(f"⚠️  目录不存在: {directory}")
    
    print(f"\n🎉 修复完成！")
    print(f"📊 总计检查 {total_count} 个文件，修复了 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
