import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/api': resolve(__dirname, './api'),
    },
  },
  server: {
    port: 5174,
    proxy: {
      '/api/': {
        target: 'http://localhost:5002',
        changeOrigin: true,
      },
    }
  }
})