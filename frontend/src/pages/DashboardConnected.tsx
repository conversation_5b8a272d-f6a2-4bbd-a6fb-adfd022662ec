import { useState, useEffect } from 'react';
import MainLayout from '../components/MainLayout';
import SkillRadarChart from '../components/SkillRadarChart';
import ProgressCard from '../components/ProgressCard';
import { Clock, BookOpen, Award, Target } from 'lucide-react';
import { assessmentAPI } from '../api';

// 定义类型接口
interface DashboardData {
  tech_skills_avg: number;
  soft_skills_avg: number;
  week_learning_hours: number;
  goal_completion_rate: number;
  recent_activities: Activity[];
  tech_skills: Skill[];
  soft_skills: Skill[];
  goals: Goal[];
}

interface Activity {
  type: string;
  title: string;
  timestamp: string;
}

interface Skill {
  subject: string;
  score: number;
  fullMark: number;
}

interface Goal {
  title: string;
  current: number;
  total: number;
  dueDate: string | null;
  category: string;
}

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    tech_skills_avg: 0,
    soft_skills_avg: 0,
    week_learning_hours: 0,
    goal_completion_rate: 0,
    recent_activities: [],
    tech_skills: [],
    soft_skills: [],
    goals: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await assessmentAPI.getDashboardData();
        setDashboardData(response.data);
      } catch (error) {
        console.error('获取仪表盘数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // 格式化最近活动时间
  const formatActivityTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      return `${diffDays}天前 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'tech_skill':
        return <Award className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'learning':
        return <BookOpen className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      case 'goal':
        return <Target className="h-4 w-4 text-purple-600 dark:text-purple-400" />;
      default:
        return <BookOpen className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  return (
    <MainLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">开发者活动仪表盘</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          跟踪您的技能发展、项目进度和学习路径
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* 状态概览卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex items-center">
              <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 mr-4">
                <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">本周学习时间</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {dashboardData.week_learning_hours.toFixed(1)}小时
                </p>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex items-center">
              <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-3 mr-4">
                <BookOpen className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">技术技能</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {dashboardData.tech_skills.length}项
                </p>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex items-center">
              <div className="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3 mr-4">
                <Award className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">技能平均分</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {dashboardData.tech_skills_avg.toFixed(1)}/5
                </p>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex items-center">
              <div className="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3 mr-4">
                <Target className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">目标完成率</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {(dashboardData.goal_completion_rate * 100).toFixed(0)}%
                </p>
              </div>
            </div>
          </div>

          {/* 主要内容区 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：技能雷达图 */}
            <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
              <SkillRadarChart 
                data={dashboardData.tech_skills} 
                title="技术能力评估" 
              />
              <SkillRadarChart 
                data={dashboardData.soft_skills} 
                title="软技能评估" 
              />
            </div>

            {/* 右侧：目标进度卡片 */}
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">目标追踪</h2>
              {dashboardData.goals.map((goal, index) => (
                <ProgressCard
                  key={index}
                  title={goal.title}
                  current={goal.current}
                  total={goal.total}
                  dueDate={goal.dueDate || undefined}
                  category={goal.category}
                />
              ))}
            </div>
          </div>

          {/* 底部区域：最近活动 */}
          <div className="mt-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近活动</h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
              {dashboardData.recent_activities.length > 0 ? (
                dashboardData.recent_activities.map((activity, index) => (
                  <div key={index} className={`p-4 ${
                    index < dashboardData.recent_activities.length - 1 ? 'border-b border-gray-200 dark:border-gray-700' : ''
                  }`}>
                    <div className="flex items-center">
                      <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {activity.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatActivityTime(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  暂无活动记录
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </MainLayout>
  );
};

export default Dashboard;
