import React, { useState, useEffect } from 'react';
import MainLayout from '../components/MainLayout';
import SkillRadar<PERSON>hart from '../components/SkillRadarChart';
import ProgressCard from '../components/ProgressCard';
import { assessmentAPI } from '../api';
import { Clock, Calendar, BookOpen, Award, Target, TrendingUp, Users, Shield, Heart, Briefcase } from 'lucide-react';
import axios from 'axios';

interface DashboardConfig {
  title: string;
  description: string;
  modules: Array<{
    name: string;
    display_name: string;
    enabled: boolean;
  }>;
}

interface DashboardData {
  tech_skills: Array<{ subject: string; score: number; fullMark: number }>;
  soft_skills: Array<{ subject: string; score: number; fullMark: number }>;
  goals: Array<{ title: string; current: number; total: number; dueDate: string; category: string }>;
  recent_activities: Array<{ title: string; type: string; date: string; hours?: number; status?: string }>;
  health_metrics: Array<{ type: string; value: number; date: string }>;
  summary: {
    total_projects: number;
    total_learning_hours: number;
    completed_goals: number;
    ethics_compliance: number;
  };
}

interface UniversalDashboardProps {
  professionType?: string;
}

const UniversalDashboard: React.FC<UniversalDashboardProps> = ({ 
  professionType = 'ai_engineer' 
}) => {
  const [dashboardConfig, setDashboardConfig] = useState<DashboardConfig | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardConfig();
    loadDashboardData();
  }, [professionType]);

  const loadDashboardConfig = async () => {
    try {
      const response = await axios.get(`/api/profession/dashboard-config/${professionType}`);
      setDashboardConfig(response.data.config);
    } catch (error) {
      console.error('加载仪表盘配置失败:', error);
      // 使用默认配置
      setDashboardConfig({
        title: '通用能力仪表盘',
        description: '全面跟踪您的技能发展、项目进度和学习路径',
        modules: [
          { name: 'tech_skills', display_name: '技术能力', enabled: true },
          { name: 'soft_skills', display_name: '软技能', enabled: true },
          { name: 'projects', display_name: '项目经验', enabled: true },
          { name: 'learning', display_name: '学习活动', enabled: true },
          { name: 'career_goals', display_name: '职业目标', enabled: true },
          { name: 'industry_knowledge', display_name: '行业知识', enabled: true },
          { name: 'ethics', display_name: '伦理合规', enabled: true },
          { name: 'health', display_name: '健康动力', enabled: true }
        ]
      });
    }
  };

  const loadDashboardData = async () => {
    try {
      const response = await assessmentAPI.getDashboardData();
      setDashboardData(response.data);
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      // 使用默认数据
      setDashboardData({
        tech_skills: [
          { subject: '核心技能1', score: 4, fullMark: 5 },
          { subject: '核心技能2', score: 3.5, fullMark: 5 },
          { subject: '核心技能3', score: 3, fullMark: 5 },
          { subject: '核心技能4', score: 2.5, fullMark: 5 },
          { subject: '核心技能5', score: 4.5, fullMark: 5 },
          { subject: '核心技能6', score: 3, fullMark: 5 },
        ],
        soft_skills: [
          { subject: '沟通表达', score: 4, fullMark: 5 },
          { subject: '团队协作', score: 4.5, fullMark: 5 },
          { subject: '问题解决', score: 3, fullMark: 5 },
          { subject: '学习能力', score: 3.5, fullMark: 5 },
          { subject: '创新思维', score: 4, fullMark: 5 },
        ],
        goals: [
          { title: "提升核心技能", current: 3, total: 5, dueDate: "2025-06-30", category: "短期目标" },
          { title: "完成重要项目", current: 2, total: 5, dueDate: "2025-07-15", category: "项目目标" },
          { title: "技能认证", current: 4, total: 10, dueDate: "2025-08-01", category: "能力提升" }
        ],
        recent_activities: [
          { title: "完成了专业课程学习", type: "课程", date: "2024-01-15", hours: 3, status: "已完成" },
          { title: "更新了技能评分", type: "技能评估", date: "2024-01-14", status: "已完成" },
          { title: "设定了新目标", type: "目标设定", date: "2024-01-13", status: "进行中" }
        ],
        health_metrics: [
          { type: "工作节奏", value: 7, date: "2024-01-15" },
          { type: "学习状态", value: 3, date: "2024-01-15" },
          { type: "动力水平", value: 8, date: "2024-01-15" }
        ],
        summary: {
          total_projects: 5,
          total_learning_hours: 120,
          completed_goals: 3,
          ethics_compliance: 8
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const getModuleIcon = (moduleName: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'tech_skills': <Award className="h-6 w-6" />,
      'soft_skills': <Users className="h-6 w-6" />,
      'projects': <Briefcase className="h-6 w-6" />,
      'learning': <BookOpen className="h-6 w-6" />,
      'career_goals': <Target className="h-6 w-6" />,
      'industry_knowledge': <TrendingUp className="h-6 w-6" />,
      'ethics': <Shield className="h-6 w-6" />,
      'health': <Heart className="h-6 w-6" />
    };
    return iconMap[moduleName] || <Award className="h-6 w-6" />;
  };

  const getModuleColor = (moduleName: string) => {
    const colorMap: { [key: string]: string } = {
      'tech_skills': 'blue-600',
      'soft_skills': 'green-600',
      'projects': 'purple-600',
      'learning': 'yellow-600',
      'career_goals': 'red-600',
      'industry_knowledge': 'indigo-600',
      'ethics': 'pink-600',
      'health': 'orange-600'
    };
    return colorMap[moduleName] || 'blue-600';
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!dashboardData || !dashboardConfig) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">暂无数据</p>
        </div>
      </MainLayout>
    );
  }

  const averageHealthScore = dashboardData.health_metrics.length > 0
    ? dashboardData.health_metrics.reduce((sum, metric) => sum + metric.value, 0) / dashboardData.health_metrics.length
    : 0;

  const enabledModules = dashboardConfig.modules.filter(module => module.enabled);

  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardConfig.title}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {dashboardConfig.description}
          </p>
        </div>

        {/* 核心指标概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">学习时间</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.total_learning_hours}小时</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">项目经验</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.total_projects}个</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">完成目标</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.completed_goals}个</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">健康指数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averageHealthScore.toFixed(1)}/10</p>
              </div>
            </div>
          </div>
        </div>

        {/* 能力模块概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {enabledModules.map((module) => (
            <div key={module.name} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">{module.display_name}</h3>
                <div className={`text-${getModuleColor(module.name)}`}>
                  {getModuleIcon(module.name)}
                </div>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {module.name === 'tech_skills' ? dashboardData.tech_skills.length :
                 module.name === 'soft_skills' ? dashboardData.soft_skills.length :
                 module.name === 'projects' ? dashboardData.summary.total_projects :
                 module.name === 'learning' ? dashboardData.recent_activities.length :
                 module.name === 'career_goals' ? dashboardData.goals.length :
                 module.name === 'ethics' ? dashboardData.summary.ethics_compliance :
                 '—'}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">项数据</p>
            </div>
          ))}
        </div>

        {/* 主要内容区 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* 左侧：技能雷达图 */}
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            {dashboardData.tech_skills.length > 0 && (
              <SkillRadarChart data={dashboardData.tech_skills} title="技术能力评估" />
            )}
            {dashboardData.soft_skills.length > 0 && (
              <SkillRadarChart data={dashboardData.soft_skills} title="软技能评估" />
            )}
          </div>

          {/* 右侧：目标进度卡片 */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">目标追踪</h2>
            {dashboardData.goals.map((goal, index) => (
              <ProgressCard
                key={index}
                title={goal.title}
                current={goal.current}
                total={goal.total}
                dueDate={goal.dueDate}
                category={goal.category}
              />
            ))}
            {dashboardData.goals.length === 0 && (
              <div className="text-center py-8">
                <Target className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">暂无目标</p>
              </div>
            )}
          </div>
        </div>

        {/* 底部区域：最近活动 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">最近活动</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {dashboardData.recent_activities.map((activity, index) => (
              <div key={index} className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {activity.type === '课程' && <BookOpen className="h-5 w-5 text-blue-600" />}
                    {activity.type === '技能评估' && <Award className="h-5 w-5 text-green-600" />}
                    {activity.type === '目标设定' && <Target className="h-5 w-5 text-purple-600" />}
                    {activity.type === '项目' && <Briefcase className="h-5 w-5 text-orange-600" />}
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </p>
                    <div className="flex items-center space-x-4 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.date).toLocaleDateString('zh-CN')}
                      </p>
                      {activity.hours && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {activity.hours}小时
                        </p>
                      )}
                      {activity.status && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === '已完成' ? 'bg-green-100 text-green-800' :
                          activity.status === '进行中' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.status}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {dashboardData.recent_activities.length === 0 && (
              <div className="text-center py-12">
                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无最近活动</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  开始添加学习活动和目标
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default UniversalDashboard;
