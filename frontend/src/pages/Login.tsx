import { useState } from 'react';
import { authAPI } from './src/api';

const Login = ({ onLoginSuccess }: { onLoginSuccess: (redirectPath?: string) => void }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const res = await authAPI.login({ username, password });
      localStorage.setItem('token', res.data.token);
      localStorage.setItem('user', JSON.stringify({ username: res.data.username, user_id: res.data.user_id }));
      // 跳转到原目标页面或首页
      const params = new URLSearchParams(window.location.search);
      const redirect = params.get('redirect');
      onLoginSuccess(redirect || '/');
    } catch (err: any) {
      setError(err?.response?.data?.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
      <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-96">
        <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">登录</h2>
        {error && <div className="mb-4 text-red-600 text-center">{error}</div>}
        <div className="mb-4">
          <label className="block mb-1 text-gray-700 dark:text-gray-300">用户名</label>
          <input type="text" value={username} onChange={e => setUsername(e.target.value)} required className="w-full p-2 border rounded" />
        </div>
        <div className="mb-6">
          <label className="block mb-1 text-gray-700 dark:text-gray-300">密码</label>
          <input type="password" value={password} onChange={e => setPassword(e.target.value)} required className="w-full p-2 border rounded" />
        </div>
        <button type="submit" disabled={loading} className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-semibold">
          {loading ? '登录中...' : '登录'}
        </button>
      </form>
    </div>
  );
};

export default Login; 