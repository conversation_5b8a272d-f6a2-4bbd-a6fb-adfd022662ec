import { useState, useEffect } from 'react';
import MainLayout from './MainLayout';
import { assessmentAPI } from './src/api';
import { Plus, Edit, Trash2, Save, X, Shield, CheckCircle, AlertCircle, FileText } from 'lucide-react';

interface EthicsCheck {
  id?: number;
  check_item: string;
  status: boolean;
  practice_case: string;
}

const EthicsPage = () => {
  const [checks, setChecks] = useState<EthicsCheck[]>([]);
  const [isAddingCheck, setIsAddingCheck] = useState(false);
  const [editingCheck, setEditingCheck] = useState<number | null>(null);
  const [newCheck, setNewCheck] = useState<EthicsCheck>({
    check_item: '',
    status: false,
    practice_case: ''
  });

  const defaultCheckItems = [
    '数据隐私保护：是否确保用户数据的安全和隐私？',
    '算法公平性：AI模型是否避免了偏见和歧视？',
    '透明度原则：是否向用户说明AI系统的工作原理？',
    '责任归属：是否明确AI系统决策的责任主体？',
    '安全可靠：AI系统是否经过充分测试和验证？',
    '用户同意：是否获得用户对数据使用的明确同意？',
    '可解释性：AI决策过程是否可以被理解和解释？',
    '持续监控：是否建立了AI系统的持续监控机制？',
    '伦理审查：项目是否经过伦理委员会审查？',
    '法规遵循：是否符合相关法律法规要求？'
  ];

  useEffect(() => {
    loadChecks();
  }, []);

  const loadChecks = async () => {
    try {
      const response = await assessmentAPI.getEthicsChecks();
      setChecks(response.data);
    } catch (error) {
      console.error('加载伦理检查失败:', error);
    }
  };

  const handleAddCheck = async () => {
    try {
      await assessmentAPI.addEthicsCheck(newCheck);
      setIsAddingCheck(false);
      setNewCheck({
        check_item: '',
        status: false,
        practice_case: ''
      });
      loadChecks();
    } catch (error) {
      console.error('添加伦理检查失败:', error);
    }
  };

  const handleUpdateCheck = async (id: number, updatedCheck: EthicsCheck) => {
    try {
      await assessmentAPI.updateEthicsCheck(id, updatedCheck);
      setEditingCheck(null);
      loadChecks();
    } catch (error) {
      console.error('更新伦理检查失败:', error);
    }
  };

  const handleToggleStatus = async (check: EthicsCheck) => {
    if (check.id) {
      await handleUpdateCheck(check.id, { ...check, status: !check.status });
    }
  };

  const addDefaultItem = (item: string) => {
    setNewCheck({ ...newCheck, check_item: item });
    setIsAddingCheck(true);
  };

  const completedChecks = checks.filter(check => check.status).length;
  const complianceRate = checks.length > 0 ? Math.round((completedChecks / checks.length) * 100) : 0;
  const withCases = checks.filter(check => check.practice_case && check.practice_case.trim() !== '').length;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">伦理与合规自检</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              确保AI开发符合伦理标准和法规要求
            </p>
          </div>
          <button
            onClick={() => setIsAddingCheck(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加检查项
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">检查项总数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{checks.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已完成</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{completedChecks}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">合规率</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{complianceRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">有案例</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{withCases}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 合规进度条 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">合规进度</h3>
          <div className="w-full bg-gray-200 rounded-full h-4">
            <div
              className={`h-4 rounded-full ${complianceRate >= 80 ? 'bg-green-500' : complianceRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
              style={{ width: `${complianceRate}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {complianceRate >= 80 ? '合规状况良好' : complianceRate >= 60 ? '需要改进' : '存在合规风险'}
          </p>
        </div>

        {/* 添加检查项表单 */}
        {isAddingCheck && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加伦理检查项</h3>
            
            {/* 快速选择默认项 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                快速选择常用检查项
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {defaultCheckItems.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => addDefaultItem(item)}
                    className="text-left p-2 text-sm text-blue-600 hover:bg-blue-50 rounded border border-blue-200"
                  >
                    {item}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  检查项内容
                </label>
                <textarea
                  value={newCheck.check_item}
                  onChange={(e) => setNewCheck({...newCheck, check_item: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="描述需要检查的伦理或合规要求..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  实践案例
                </label>
                <textarea
                  value={newCheck.practice_case}
                  onChange={(e) => setNewCheck({...newCheck, practice_case: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="描述具体的实践案例或实施方法..."
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="status"
                  checked={newCheck.status}
                  onChange={(e) => setNewCheck({...newCheck, status: e.target.checked})}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="status" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  已完成此检查项
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingCheck(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddCheck}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 检查项列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">伦理检查清单</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {checks.map((check) => (
              <div key={check.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      <button
                        onClick={() => handleToggleStatus(check)}
                        className={`h-5 w-5 rounded border-2 flex items-center justify-center ${
                          check.status 
                            ? 'bg-green-500 border-green-500 text-white' 
                            : 'border-gray-300 hover:border-green-500'
                        }`}
                      >
                        {check.status && <CheckCircle className="h-3 w-3" />}
                      </button>
                    </div>
                    <div className="flex-1">
                      <h4 className={`text-base font-medium ${
                        check.status ? 'text-green-700 dark:text-green-400' : 'text-gray-900 dark:text-white'
                      }`}>
                        {check.check_item}
                      </h4>
                      {check.practice_case && (
                        <div className="mt-2">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">实践案例：</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {check.practice_case}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => setEditingCheck(check.id!)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {checks.length === 0 && (
          <div className="text-center py-12">
            <Shield className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无伦理检查项</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始添加伦理和合规检查项目
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default EthicsPage;
