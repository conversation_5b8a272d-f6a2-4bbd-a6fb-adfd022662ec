import { useState, useEffect } from 'react';
import MainLayout from './MainLayout';
import { assessmentAPI } from './src/api';
import { Plus, Edit, Trash2, Save, X, Heart, Activity, Users, Zap, TrendingUp, TrendingDown } from 'lucide-react';

interface HealthMetric {
  id?: number;
  metric_type: string;
  value: number;
  description: string;
  date: string;
}

const HealthPage = () => {
  const [metrics, setMetrics] = useState<HealthMetric[]>([]);
  const [isAddingMetric, setIsAddingMetric] = useState(false);
  const [editingMetric, setEditingMetric] = useState<number | null>(null);
  const [newMetric, setNewMetric] = useState<HealthMetric>({
    metric_type: '工作节奏',
    value: 5,
    description: '',
    date: new Date().toISOString().split('T')[0]
  });

  const metricTypes = [
    { name: '工作节奏', icon: Activity, color: 'blue' },
    { name: '学习倦怠', icon: TrendingDown, color: 'red' },
    { name: '社交充电', icon: Users, color: 'green' },
    { name: '身体信号', icon: Heart, color: 'purple' },
    { name: '动力水平', icon: Zap, color: 'yellow' },
    { name: '压力指数', icon: TrendingUp, color: 'orange' }
  ];

  useEffect(() => {
    loadMetrics();
  }, []);

  const loadMetrics = async () => {
    try {
      const response = await assessmentAPI.getHealthMetrics();
      setMetrics(response.data);
    } catch (error) {
      console.error('加载健康指标失败:', error);
    }
  };

  const handleAddMetric = async () => {
    try {
      await assessmentAPI.addHealthMetric(newMetric);
      setIsAddingMetric(false);
      setNewMetric({
        metric_type: '工作节奏',
        value: 5,
        description: '',
        date: new Date().toISOString().split('T')[0]
      });
      loadMetrics();
    } catch (error) {
      console.error('添加健康指标失败:', error);
    }
  };

  const handleUpdateMetric = async (id: number, updatedMetric: HealthMetric) => {
    try {
      await assessmentAPI.updateHealthMetric(id, updatedMetric);
      setEditingMetric(null);
      loadMetrics();
    } catch (error) {
      console.error('更新健康指标失败:', error);
    }
  };

  const getMetricColor = (type: string) => {
    const metricType = metricTypes.find(mt => mt.name === type);
    return metricType ? metricType.color : 'gray';
  };

  const getMetricIcon = (type: string) => {
    const metricType = metricTypes.find(mt => mt.name === type);
    const IconComponent = metricType ? metricType.icon : Activity;
    return <IconComponent className="h-4 w-4" />;
  };

  const getValueColor = (value: number) => {
    if (value >= 8) return 'text-green-600';
    if (value >= 6) return 'text-yellow-600';
    if (value >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  const getValueBg = (value: number) => {
    if (value >= 8) return 'bg-green-100';
    if (value >= 6) return 'bg-yellow-100';
    if (value >= 4) return 'bg-orange-100';
    return 'bg-red-100';
  };

  // 计算各类型指标的平均值
  const getAverageByType = (type: string) => {
    const typeMetrics = metrics.filter(m => m.metric_type === type);
    if (typeMetrics.length === 0) return 0;
    return typeMetrics.reduce((sum, m) => sum + m.value, 0) / typeMetrics.length;
  };

  // 获取最近7天的数据
  const getRecentMetrics = () => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return metrics.filter(m => new Date(m.date) >= sevenDaysAgo);
  };

  const recentMetrics = getRecentMetrics();
  const overallAverage = metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length : 0;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">健康与动力评估</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              监控工作状态，保持身心健康和学习动力
            </p>
          </div>
          <button
            onClick={() => setIsAddingMetric(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            记录状态
          </button>
        </div>

        {/* 健康指标概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
          {metricTypes.map((type) => {
            const average = getAverageByType(type.name);
            const IconComponent = type.icon;
            return (
              <div key={type.name} className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div className="flex items-center justify-between mb-2">
                  <IconComponent className={`h-6 w-6 text-${type.color}-600`} />
                  <span className={`text-2xl font-bold ${getValueColor(average)}`}>
                    {average.toFixed(1)}
                  </span>
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{type.name}</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className={`h-2 rounded-full bg-${type.color}-500`}
                    style={{ width: `${(average / 10) * 100}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 整体健康状况 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">整体健康状况</h3>
          <div className="flex items-center space-x-6">
            <div className="flex items-center">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center ${getValueBg(overallAverage)}`}>
                <span className={`text-2xl font-bold ${getValueColor(overallAverage)}`}>
                  {overallAverage.toFixed(1)}
                </span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">综合评分</p>
                <p className={`text-lg font-semibold ${getValueColor(overallAverage)}`}>
                  {overallAverage >= 8 ? '优秀' : overallAverage >= 6 ? '良好' : overallAverage >= 4 ? '一般' : '需要关注'}
                </p>
              </div>
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                最近7天记录了 {recentMetrics.length} 次状态评估
              </p>
              <div className="w-full bg-gray-200 rounded-full h-3 mt-2">
                <div
                  className={`h-3 rounded-full ${overallAverage >= 8 ? 'bg-green-500' : overallAverage >= 6 ? 'bg-yellow-500' : overallAverage >= 4 ? 'bg-orange-500' : 'bg-red-500'}`}
                  style={{ width: `${(overallAverage / 10) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* 添加健康指标表单 */}
        {isAddingMetric && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">记录健康状态</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  指标类型
                </label>
                <select
                  value={newMetric.metric_type}
                  onChange={(e) => setNewMetric({...newMetric, metric_type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {metricTypes.map(type => (
                    <option key={type.name} value={type.name}>{type.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  日期
                </label>
                <input
                  type="date"
                  value={newMetric.date}
                  onChange={(e) => setNewMetric({...newMetric, date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  评分 (1-10分)
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={newMetric.value}
                    onChange={(e) => setNewMetric({...newMetric, value: parseInt(e.target.value)})}
                    className="flex-1"
                  />
                  <span className={`text-2xl font-bold ${getValueColor(newMetric.value)}`}>
                    {newMetric.value}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>很差</span>
                  <span>一般</span>
                  <span>很好</span>
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  详细描述
                </label>
                <textarea
                  value={newMetric.description}
                  onChange={(e) => setNewMetric({...newMetric, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="描述当前的状态、感受或需要注意的事项..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingMetric(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddMetric}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 健康记录列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">健康记录</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {metrics.slice(0, 10).map((metric) => (
              <div key={metric.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="flex-shrink-0">
                      {getMetricIcon(metric.metric_type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-base font-medium text-gray-900 dark:text-white">
                          {metric.metric_type}
                        </h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getValueBg(metric.value)} ${getValueColor(metric.value)}`}>
                          {metric.value}/10
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {metric.date}
                        </span>
                      </div>
                      {metric.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {metric.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => setEditingMetric(metric.id!)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {metrics.length === 0 && (
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无健康记录</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始记录您的健康状态和工作动力
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default HealthPage;
