import { useState, useEffect } from 'react';
import MainLayout from '../components/MainLayout';
import SkillRadarChart from '../components/SkillRadarChart';
import { skillsAPI } from '../api';
import { Plus, Edit, Trash2, Save, X, Users, MessageCircle, Lightbulb, Target } from 'lucide-react';

interface SoftSkill {
  id?: number;
  name: string;
  score: number;
  evidence: string;
}

const SoftSkillsPage = () => {
  const [skills, setSkills] = useState<SoftSkill[]>([]);
  const [isAddingSkill, setIsAddingSkill] = useState(false);
  const [editingSkill, setEditingSkill] = useState<number | null>(null);
  const [newSkill, setNewSkill] = useState<SoftSkill>({
    name: '',
    score: 1,
    evidence: ''
  });

  const defaultSoftSkills = [
    '沟通表达', '团队协作', '领导力', '问题解决', '创新思维',
    '时间管理', '学习能力', '适应能力', '批判性思维', '情商管理',
    '项目管理', '客户服务', '谈判技巧', '演讲能力', '冲突解决'
  ];

  useEffect(() => {
    loadSkills();
  }, []);

  const loadSkills = async () => {
    try {
      const response = await skillsAPI.getSoftSkills();
      setSkills(response.data);
    } catch (error) {
      console.error('加载软技能失败:', error);
    }
  };

  const handleAddSkill = async () => {
    try {
      await skillsAPI.addSoftSkill(newSkill);
      setIsAddingSkill(false);
      setNewSkill({
        name: '',
        score: 1,
        evidence: ''
      });
      loadSkills();
    } catch (error) {
      console.error('添加软技能失败:', error);
    }
  };

  const handleUpdateSkill = async (id: number, updatedSkill: SoftSkill) => {
    try {
      await skillsAPI.updateSoftSkill(id, updatedSkill);
      setEditingSkill(null);
      loadSkills();
    } catch (error) {
      console.error('更新软技能失败:', error);
    }
  };

  const handleDeleteSkill = async (id: number) => {
    if (window.confirm('确定要删除这个软技能吗？')) {
      try {
        await skillsAPI.deleteSoftSkill(id);
        loadSkills();
      } catch (error) {
        console.error('删除软技能失败:', error);
      }
    }
  };

  const addDefaultSkill = (skillName: string) => {
    setNewSkill({ ...newSkill, name: skillName });
    setIsAddingSkill(true);
  };

  const getScoreColor = (score: number) => {
    if (score >= 4) return 'text-green-600';
    if (score >= 3) return 'text-yellow-600';
    if (score >= 2) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 4) return 'bg-green-100';
    if (score >= 3) return 'bg-yellow-100';
    if (score >= 2) return 'bg-orange-100';
    return 'bg-red-100';
  };

  const getSkillIcon = (skillName: string) => {
    if (skillName.includes('沟通') || skillName.includes('表达')) return <MessageCircle className="h-4 w-4" />;
    if (skillName.includes('团队') || skillName.includes('协作')) return <Users className="h-4 w-4" />;
    if (skillName.includes('创新') || skillName.includes('思维')) return <Lightbulb className="h-4 w-4" />;
    if (skillName.includes('领导') || skillName.includes('管理')) return <Target className="h-4 w-4" />;
    return <Target className="h-4 w-4" />;
  };

  // 准备雷达图数据
  const radarData = skills.map(skill => ({
    subject: skill.name,
    score: skill.score,
    fullMark: 5
  }));

  const averageScore = skills.length > 0 ? skills.reduce((sum, skill) => sum + skill.score, 0) / skills.length : 0;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">软技能雷达图</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              评估和提升软技能，增强职场竞争力
            </p>
          </div>
          <button
            onClick={() => setIsAddingSkill(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加软技能
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">软技能总数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{skills.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">平均评分</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averageScore.toFixed(1)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Lightbulb className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">优秀技能</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {skills.filter(skill => skill.score >= 4).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 雷达图 */}
        {skills.length > 0 && (
          <div className="mb-6">
            <SkillRadarChart data={radarData} title="软技能雷达图" />
          </div>
        )}

        {/* 添加软技能表单 */}
        {isAddingSkill && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加软技能</h3>
            
            {/* 快速选择默认技能 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                快速选择常用软技能
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                {defaultSoftSkills.map((skillName, index) => (
                  <button
                    key={index}
                    onClick={() => addDefaultSkill(skillName)}
                    className="text-left p-2 text-sm text-blue-600 hover:bg-blue-50 rounded border border-blue-200"
                  >
                    {skillName}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  技能名称
                </label>
                <input
                  type="text"
                  value={newSkill.name}
                  onChange={(e) => setNewSkill({...newSkill, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：沟通表达"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  评分 (1-5分)
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={newSkill.score}
                    onChange={(e) => setNewSkill({...newSkill, score: parseInt(e.target.value)})}
                    className="flex-1"
                  />
                  <span className={`text-2xl font-bold ${getScoreColor(newSkill.score)}`}>
                    {newSkill.score}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>基础了解</span>
                  <span>熟练掌握</span>
                  <span>专家水平</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  证据案例
                </label>
                <textarea
                  value={newSkill.evidence}
                  onChange={(e) => setNewSkill({...newSkill, evidence: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="描述能力证明的具体案例或项目..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingSkill(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddSkill}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 软技能列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">软技能清单</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {skills.map((skill) => (
              <div key={skill.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="flex-shrink-0">
                      {getSkillIcon(skill.name)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                          {skill.name}
                        </h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreBg(skill.score)} ${getScoreColor(skill.score)}`}>
                          {skill.score}/5
                        </span>
                      </div>
                      {skill.evidence && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <strong>证据案例：</strong>{skill.evidence}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => setEditingSkill(skill.id!)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button 
                      onClick={() => handleDeleteSkill(skill.id!)}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {skills.length === 0 && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无软技能记录</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始添加您的软技能评估
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default SoftSkillsPage;
