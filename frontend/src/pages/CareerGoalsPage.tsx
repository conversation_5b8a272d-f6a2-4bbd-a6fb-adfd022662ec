import { useState, useEffect } from 'react';
import MainLayout from './MainLayout';
import { assessmentAPI } from './src/api';
import { Plus, Edit, Trash2, Save, X, Target, Calendar, TrendingUp, AlertTriangle } from 'lucide-react';

interface CareerGoal {
  id?: number;
  title: string;
  goal_type: string;
  description: string;
  progress: number;
  resource_gap: string;
  target_date: string;
}

const CareerGoalsPage = () => {
  const [goals, setGoals] = useState<CareerGoal[]>([]);
  const [isAddingGoal, setIsAddingGoal] = useState(false);
  const [editingGoal, setEditingGoal] = useState<number | null>(null);
  const [newGoal, setNewGoal] = useState<CareerGoal>({
    title: '',
    goal_type: '短期目标',
    description: '',
    progress: 0,
    resource_gap: '',
    target_date: ''
  });

  const goalTypes = ['短期目标', '长期目标', '技能提升', '职位晋升', '项目目标'];

  useEffect(() => {
    loadGoals();
  }, []);

  const loadGoals = async () => {
    try {
      const response = await assessmentAPI.getCareerGoals();
      setGoals(response.data);
    } catch (error) {
      console.error('加载职业目标失败:', error);
    }
  };

  const handleAddGoal = async () => {
    try {
      await assessmentAPI.addCareerGoal(newGoal);
      setIsAddingGoal(false);
      setNewGoal({
        title: '',
        goal_type: '短期目标',
        description: '',
        progress: 0,
        resource_gap: '',
        target_date: ''
      });
      loadGoals();
    } catch (error) {
      console.error('添加职业目标失败:', error);
    }
  };

  const handleUpdateProgress = async (goalId: number, newProgress: number) => {
    try {
      await assessmentAPI.updateGoalProgress(goalId, { progress: newProgress });
      loadGoals();
    } catch (error) {
      console.error('更新目标进度失败:', error);
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    if (progress >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case '短期目标': return 'bg-blue-100 text-blue-800';
      case '长期目标': return 'bg-purple-100 text-purple-800';
      case '技能提升': return 'bg-green-100 text-green-800';
      case '职位晋升': return 'bg-yellow-100 text-yellow-800';
      case '项目目标': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case '短期目标': return <Target className="h-4 w-4" />;
      case '长期目标': return <TrendingUp className="h-4 w-4" />;
      case '技能提升': return <Target className="h-4 w-4" />;
      case '职位晋升': return <TrendingUp className="h-4 w-4" />;
      case '项目目标': return <Target className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const isOverdue = (targetDate: string) => {
    if (!targetDate) return false;
    return new Date(targetDate) < new Date();
  };

  const completedGoals = goals.filter(goal => goal.progress >= 100).length;
  const overdueGoals = goals.filter(goal => isOverdue(goal.target_date) && goal.progress < 100).length;
  const averageProgress = goals.length > 0 ? goals.reduce((sum, goal) => sum + goal.progress, 0) / goals.length : 0;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">职业发展对标</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              设定目标，追踪进度，实现职业发展规划
            </p>
          </div>
          <button
            onClick={() => setIsAddingGoal(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加目标
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总目标数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{goals.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已完成</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{completedGoals}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已逾期</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{overdueGoals}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">平均进度</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{Math.round(averageProgress)}%</p>
              </div>
            </div>
          </div>
        </div>

        {/* 添加目标表单 */}
        {isAddingGoal && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加职业目标</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  目标标题
                </label>
                <input
                  type="text"
                  value={newGoal.title}
                  onChange={(e) => setNewGoal({...newGoal, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：掌握React高级特性"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  目标类型
                </label>
                <select
                  value={newGoal.goal_type}
                  onChange={(e) => setNewGoal({...newGoal, goal_type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {goalTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  目标日期
                </label>
                <input
                  type="date"
                  value={newGoal.target_date}
                  onChange={(e) => setNewGoal({...newGoal, target_date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  初始进度 (%)
                </label>
                <input
                  type="number"
                  value={newGoal.progress}
                  onChange={(e) => setNewGoal({...newGoal, progress: parseInt(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  max="100"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  目标描述
                </label>
                <textarea
                  value={newGoal.description}
                  onChange={(e) => setNewGoal({...newGoal, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="详细描述目标内容和期望达到的效果..."
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  资源缺口分析
                </label>
                <textarea
                  value={newGoal.resource_gap}
                  onChange={(e) => setNewGoal({...newGoal, resource_gap: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="分析实现目标所需的资源、技能或条件..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingGoal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddGoal}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 目标列表 */}
        <div className="space-y-4">
          {goals.map((goal) => (
            <div key={goal.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    {getTypeIcon(goal.goal_type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {goal.title}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(goal.goal_type)}`}>
                        {goal.goal_type}
                      </span>
                      {isOverdue(goal.target_date) && goal.progress < 100 && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          已逾期
                        </span>
                      )}
                    </div>
                    {goal.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {goal.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      {goal.target_date && (
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          目标日期: {goal.target_date}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-gray-400 hover:text-gray-600">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="text-gray-400 hover:text-red-600">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* 进度条 */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">进度</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{goal.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${getProgressColor(goal.progress)}`}
                    style={{ width: `${goal.progress}%` }}
                  ></div>
                </div>
              </div>

              {/* 进度更新 */}
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm text-gray-600 dark:text-gray-400">更新进度:</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={goal.progress}
                  onChange={(e) => handleUpdateProgress(goal.id!, parseInt(e.target.value))}
                  className="flex-1"
                />
              </div>

              {goal.resource_gap && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">资源缺口分析</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{goal.resource_gap}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        {goals.length === 0 && (
          <div className="text-center py-12">
            <Target className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无职业目标</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始设定您的职业发展目标
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default CareerGoalsPage;
