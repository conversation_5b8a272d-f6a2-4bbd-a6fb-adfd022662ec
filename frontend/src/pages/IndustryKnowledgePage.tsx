import { useState, useEffect } from 'react';
import MainLayout from './MainLayout';
import { assessmentAPI } from './src/api';
import { Plus, Edit, Trash2, Save, X, Target, TrendingUp, AlertCircle } from 'lucide-react';

interface IndustryKnowledge {
  id?: number;
  industry: string;
  dimension: string;
  question: string;
  status: string;
}

const IndustryKnowledgePage = () => {
  const [knowledgeItems, setKnowledgeItems] = useState<IndustryKnowledge[]>([]);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);
  const [newItem, setNewItem] = useState<IndustryKnowledge>({
    industry: '',
    dimension: '领域知识',
    question: '',
    status: ''
  });

  const industries = [
    '金融科技', '医疗健康', '教育科技', '电商零售', '智能制造',
    '自动驾驶', '智慧城市', '游戏娱乐', '社交媒体', '企业服务'
  ];

  const dimensions = ['领域知识', '业务场景', '合规要求', '技术标准', '行业趋势'];

  useEffect(() => {
    loadKnowledgeItems();
  }, []);

  const loadKnowledgeItems = async () => {
    try {
      const response = await assessmentAPI.getIndustryKnowledge();
      setKnowledgeItems(response.data);
    } catch (error) {
      console.error('加载行业知识失败:', error);
    }
  };

  const handleAddItem = async () => {
    try {
      await assessmentAPI.addIndustryKnowledge(newItem);
      setIsAddingItem(false);
      setNewItem({
        industry: '',
        dimension: '领域知识',
        question: '',
        status: ''
      });
      loadKnowledgeItems();
    } catch (error) {
      console.error('添加行业知识失败:', error);
    }
  };

  const handleUpdateItem = async (id: number, updatedItem: IndustryKnowledge) => {
    try {
      await assessmentAPI.updateIndustryKnowledge(id, updatedItem);
      setEditingItem(null);
      loadKnowledgeItems();
    } catch (error) {
      console.error('更新行业知识失败:', error);
    }
  };

  const getDimensionColor = (dimension: string) => {
    switch (dimension) {
      case '领域知识': return 'bg-blue-100 text-blue-800';
      case '业务场景': return 'bg-green-100 text-green-800';
      case '合规要求': return 'bg-red-100 text-red-800';
      case '技术标准': return 'bg-purple-100 text-purple-800';
      case '行业趋势': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDimensionIcon = (dimension: string) => {
    switch (dimension) {
      case '领域知识': return <Target className="h-4 w-4" />;
      case '业务场景': return <TrendingUp className="h-4 w-4" />;
      case '合规要求': return <AlertCircle className="h-4 w-4" />;
      case '技术标准': return <Target className="h-4 w-4" />;
      case '行业趋势': return <TrendingUp className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const groupedItems = knowledgeItems.reduce((acc, item) => {
    if (!acc[item.industry]) {
      acc[item.industry] = [];
    }
    acc[item.industry].push(item);
    return acc;
  }, {} as Record<string, IndustryKnowledge[]>);

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">行业知识匹配度分析</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              评估目标行业的知识掌握程度，识别知识缺口
            </p>
          </div>
          <button
            onClick={() => setIsAddingItem(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加知识项
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">目标行业</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Object.keys(groupedItems).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">知识项总数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{knowledgeItems.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">待完善</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {knowledgeItems.filter(item => !item.status || item.status.trim() === '').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已评估</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {knowledgeItems.filter(item => item.status && item.status.trim() !== '').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 添加知识项表单 */}
        {isAddingItem && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加行业知识项</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  目标行业
                </label>
                <select
                  value={newItem.industry}
                  onChange={(e) => setNewItem({...newItem, industry: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">选择行业</option>
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  知识维度
                </label>
                <select
                  value={newItem.dimension}
                  onChange={(e) => setNewItem({...newItem, dimension: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {dimensions.map(dimension => (
                    <option key={dimension} value={dimension}>{dimension}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  自检问题
                </label>
                <textarea
                  value={newItem.question}
                  onChange={(e) => setNewItem({...newItem, question: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="例如：我是否了解金融科技行业的主要监管要求？"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  现状评估
                </label>
                <textarea
                  value={newItem.status}
                  onChange={(e) => setNewItem({...newItem, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="描述当前的知识掌握情况、学习进度或需要改进的地方..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingItem(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddItem}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 按行业分组显示知识项 */}
        <div className="space-y-6">
          {Object.entries(groupedItems).map(([industry, items]) => (
            <div key={industry} className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">{industry}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {items.length} 个知识项
                </p>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {items.map((item) => (
                  <div key={item.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="flex-shrink-0">
                          {getDimensionIcon(item.dimension)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDimensionColor(item.dimension)}`}>
                              {item.dimension}
                            </span>
                          </div>
                          <h4 className="text-base font-medium text-gray-900 dark:text-white mb-2">
                            {item.question}
                          </h4>
                          {item.status && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              <strong>现状：</strong>{item.status}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button 
                          onClick={() => setEditingItem(item.id!)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-gray-400 hover:text-red-600">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {Object.keys(groupedItems).length === 0 && (
          <div className="text-center py-12">
            <Target className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无行业知识项</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始添加您感兴趣的行业知识评估项目
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default IndustryKnowledgePage;
