import { ArrowRight } from 'lucide-react';

interface ProgressCardProps {
  title: string;
  current: number;
  total: number;
  dueDate?: string;
  category?: string;
}

const ProgressCard = ({ 
  title, 
  current, 
  total, 
  dueDate, 
  category = "目标" 
}: ProgressCardProps) => {
  const percentage = Math.round((current / total) * 100);
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
      <div className="flex justify-between items-start mb-2">
        <div>
          <span className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded">
            {category}
          </span>
        </div>
        {dueDate && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            截止: {dueDate}
          </span>
        )}
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-2 mb-3 line-clamp-2">
        {title}
      </h3>
      
      <div className="mb-2">
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            进度
          </span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {percentage}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full" 
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {current} / {total} 完成
        </div>
        <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center text-sm font-medium">
          查看详情
          <ArrowRight size={16} className="ml-1" />
        </button>
      </div>
    </div>
  );
};

export default ProgressCard;
