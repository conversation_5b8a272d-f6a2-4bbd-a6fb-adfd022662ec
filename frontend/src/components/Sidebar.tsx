import { useState } from 'react';
import { 
  BarChart3, 
  Code2, 
  BookOpen, 
  Briefcase, 
  Brain, 
  Activity, 
  Target, 
  ShieldCheck, 
  Heart, 
  User, 
  Settings, 
  Menu, 
  X 
} from 'lucide-react';

interface SidebarProps {
  currentPage: string;
  onPageChange: (page: string) => void;
}

const Sidebar = ({ currentPage, onPageChange }: SidebarProps) => {
  const [isOpen, setIsOpen] = useState(true);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const menuItems = [
    { icon: <BarChart3 size={20} />, label: '仪表盘', path: '/' },
    { icon: <Code2 size={20} />, label: '技术能力评估', path: '/tech-skills' },
    { icon: <Briefcase size={20} />, label: '项目经验复盘', path: '/projects' },
    { icon: <BookOpen size={20} />, label: '学习能力诊断', path: '/learning' },
    { icon: <Brain size={20} />, label: '行业知识匹配', path: '/industry' },
    { icon: <Activity size={20} />, label: '软技能雷达图', path: '/soft-skills' },
    { icon: <Target size={20} />, label: '职业发展对标', path: '/career' },
    { icon: <ShieldCheck size={20} />, label: '伦理与合规', path: '/ethics' },
    { icon: <Heart size={20} />, label: '健康与动力', path: '/health' },
  ];

  const bottomMenuItems = [
    { icon: <User size={20} />, label: '个人资料', path: '/profile' },
    { icon: <Settings size={20} />, label: '设置', path: '/settings' },
  ];

  const handleMenuClick = (path: string) => {
    onPageChange(path);
    // 在移动端点击后关闭侧边栏
    if (window.innerWidth < 1024) {
      setIsOpen(false);
    }
  };

  const isActivePage = (path: string) => {
    return currentPage === path;
  };

  return (
    <>
      {/* 移动端菜单按钮 */}
      <button
        className="fixed z-50 bottom-4 right-4 p-2 rounded-full bg-blue-600 text-white lg:hidden"
        onClick={toggleSidebar}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* 侧边栏 */}
      <aside
        className={`${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:w-64 lg:shrink-0`}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">AI开发者助手</h2>
        </div>

        {/* 侧边栏菜单 */}
        <div className="py-4 flex flex-col h-[calc(100%-4rem)]">
          <nav className="flex-1 px-2 space-y-1">
            {menuItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleMenuClick(item.path)}
                className={`w-full flex items-center px-4 py-2 text-left rounded-md group transition-colors ${
                  isActivePage(item.path)
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className={`mr-3 transition-colors ${
                  isActivePage(item.path)
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                }`}>
                  {item.icon}
                </span>
                <span>{item.label}</span>
              </button>
            ))}
          </nav>

          {/* 底部菜单 */}
          <div className="px-2 space-y-1 border-t border-gray-200 dark:border-gray-700 pt-4 mt-auto">
            {bottomMenuItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleMenuClick(item.path)}
                className={`w-full flex items-center px-4 py-2 text-left rounded-md group transition-colors ${
                  isActivePage(item.path)
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className={`mr-3 transition-colors ${
                  isActivePage(item.path)
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                }`}>
                  {item.icon}
                </span>
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
