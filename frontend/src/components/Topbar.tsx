import { <PERSON>, <PERSON>, Moon, User } from 'lucide-react';
import { useState } from 'react';

const Topbar = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    // 在实际应用中，这里会切换文档的dark类
    document.documentElement.classList.toggle('dark');
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="px-4 py-3 flex items-center justify-between">
        {/* 左侧标题 */}
        <div className="flex items-center">
          <h1 className="text-xl font-semibold text-gray-800 dark:text-white">AI应用开发者活动管理</h1>
        </div>

        {/* 右侧工具栏 */}
        <div className="flex items-center space-x-4">
          {/* 通知按钮 */}
          <button className="p-1 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700">
            <Bell size={20} />
          </button>

          {/* 暗黑模式切换 */}
          <button 
            className="p-1 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={toggleDarkMode}
          >
            {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
          </button>

          {/* 用户头像 */}
          <div className="relative">
            <button className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
              <span className="sr-only">打开用户菜单</span>
              <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300">
                <User size={16} />
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Topbar;
