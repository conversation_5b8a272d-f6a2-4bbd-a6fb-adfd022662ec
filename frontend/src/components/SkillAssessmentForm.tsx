import { useState } from 'react';
import { Slider } from '../ui/slider';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { PlusCircle, Save } from 'lucide-react';

interface SkillAssessmentProps {
  category: string;
  skills: {
    id: string;
    name: string;
    score: number;
    evidence?: string;
    priority: 'H' | 'M' | 'L';
  }[];
  onSave?: (skills: any[]) => void;
}

const SkillAssessmentForm = ({ 
  category, 
  skills: initialSkills, 
  onSave 
}: SkillAssessmentProps) => {
  const [skills, setSkills] = useState(initialSkills);

  const handleScoreChange = (id: string, value: number[]) => {
    setSkills(skills.map(skill => 
      skill.id === id ? { ...skill, score: value[0] } : skill
    ));
  };

  const handleEvidenceChange = (id: string, evidence: string) => {
    setSkills(skills.map(skill => 
      skill.id === id ? { ...skill, evidence } : skill
    ));
  };

  const handlePriorityChange = (id: string, priority: 'H' | 'M' | 'L') => {
    setSkills(skills.map(skill => 
      skill.id === id ? { ...skill, priority } : skill
    ));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(skills);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'H': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'M': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'L': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{category}技能评估</h2>
        <Button onClick={handleSave} className="flex items-center">
          <Save className="mr-2 h-4 w-4" />
          保存评估
        </Button>
      </div>

      <div className="space-y-6">
        {skills.map((skill) => (
          <div key={skill.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex flex-wrap items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">{skill.name}</h3>
              <div className="flex space-x-2 mt-2 sm:mt-0">
                {['H', 'M', 'L'].map((p) => (
                  <button
                    key={p}
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      skill.priority === p ? getPriorityColor(p) : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                    }`}
                    onClick={() => handlePriorityChange(skill.id, p as 'H' | 'M' | 'L')}
                  >
                    {p === 'H' ? '高' : p === 'M' ? '中' : '低'}优先级
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  评分 (1-5)
                </span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {skill.score}
                </span>
              </div>
              <Slider
                defaultValue={[skill.score]}
                max={5}
                min={1}
                step={1}
                onValueChange={(value) => handleScoreChange(skill.id, value)}
              />
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span>基础了解</span>
                <span>熟练掌握</span>
                <span>专家水平</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                证据案例
              </label>
              <Textarea
                placeholder="描述能力证明的具体案例或项目..."
                value={skill.evidence || ''}
                onChange={(e) => handleEvidenceChange(skill.id, e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
        ))}

        <Button variant="outline" className="w-full flex items-center justify-center">
          <PlusCircle className="mr-2 h-4 w-4" />
          添加新技能
        </Button>
      </div>
    </div>
  );
};

export default SkillAssessmentForm;
