/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const XSquare = createLucideIcon("XSquare", [
  [
    "rect",
    {
      width: "18",
      height: "18",
      x: "3",
      y: "3",
      rx: "2",
      ry: "2",
      key: "1m3agn"
    }
  ],
  ["path", { d: "m15 9-6 6", key: "1uzhvr" }],
  ["path", { d: "m9 9 6 6", key: "z0biqf" }]
]);

export { XSquare as default };
//# sourceMappingURL=x-square.mjs.map
