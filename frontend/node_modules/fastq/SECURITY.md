# 安全策略

## 支持的版本

我们目前正在为以下版本的SkillForge提供安全更新：

| 版本 | 支持状态 |
| ---- | ----------- |
| 2.0.x | ✅ |
| 1.0.x | ❌ |

## 报告漏洞

我们非常重视安全问题。如果您发现安全漏洞，请通过以下步骤报告：

1. **不要公开披露**: 请不要在公共issue tracker中报告安全漏洞
2. **发送邮件**: 请将详细信息发送至 <EMAIL>
3. **提供详情**: 请尽可能详细地描述漏洞，包括复现步骤
4. **等待回应**: 我们的安全团队将尽快回应您的报告

## 安全流程

当收到安全报告时，我们将：

1. 确认收到报告并开始调查
2. 确定漏洞的影响范围和严重性
3. 开发修复方案
4. 发布安全更新
5. 在修复后公开披露漏洞详情（除非报告者要求保密）

## 安全最佳实践

使用SkillForge时，我们建议遵循以下安全最佳实践：

1. 始终保持系统更新到最新版本
2. 使用强密码并启用双因素认证（如果可用）
3. 限制数据库和API的访问权限
4. 定期备份数据
5. 在生产环境中使用HTTPS

感谢您帮助我们保持SkillForge的安全！
