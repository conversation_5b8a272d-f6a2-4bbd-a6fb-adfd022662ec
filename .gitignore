# 临时删除文件夹
_del/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/
venv/
ENV/
env.bak/
venv.bak/

# Flask
instance/
.webassets-cache
flask_session/

# Node.js
node_modules/
frontend/node_modules/
npm-debug.log
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.eslintcache
.node_repl_history
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Build files
/dist
/build
/out
/.next/
/.nuxt/
/.vuepress/dist
/.serverless/
.fusebox/
.dynamodb/
.tern-port
.cache/
.parcel-cache

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# Database
*.sqlite
*.sqlite3
*.db
backend/*.sqlite
backend/*.sqlite3
backend/*.db
backend/*.backup

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
data/
!data/.gitkeep

# PID files
.backend.pid
.frontend.pid