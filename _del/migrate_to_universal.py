#!/usr/bin/env python3
"""
数据库迁移脚本：从AI工程师专用系统迁移到通用职业管理平台
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import app
from assessment import db, TechSkill, SoftSkill, Project, LearningActivity, CareerGoal, IndustryKnowledge, EthicsCheck, HealthMetric, ProfessionTemplate, SkillCategory
from user import User
from profession_templates import PROFESSION_TEMPLATES

def create_profession_templates():
    """创建职业模板数据"""
    print("🔧 创建职业模板...")
    
    for name, config in PROFESSION_TEMPLATES.items():
        # 检查是否已存在
        existing = ProfessionTemplate.query.filter_by(name=name).first()
        if existing:
            print(f"  ⚠️  职业模板 {name} 已存在，跳过")
            continue
        
        # 创建新的职业模板
        template = ProfessionTemplate(
            name=name,
            display_name=config['display_name'],
            description=config['description'],
            config=config['config'],
            is_active=True
        )
        db.session.add(template)
        print(f"  ✅ 创建职业模板: {config['display_name']}")
    
    db.session.commit()
    print("✅ 职业模板创建完成")

def create_skill_categories():
    """创建技能分类数据"""
    print("🔧 创建技能分类...")
    
    for name, config in PROFESSION_TEMPLATES.items():
        template = ProfessionTemplate.query.filter_by(name=name).first()
        if not template:
            continue
        
        # 创建技术技能分类
        for category in config['config'].get('tech_skill_categories', []):
            existing = SkillCategory.query.filter_by(
                profession_template_id=template.id,
                category_type='tech',
                name=category['name']
            ).first()
            
            if existing:
                continue
            
            skill_category = SkillCategory(
                profession_template_id=template.id,
                category_type='tech',
                name=category['name'],
                display_name=category['display_name'],
                description=category.get('description', ''),
                sort_order=0,
                is_active=True
            )
            db.session.add(skill_category)
        
        # 创建软技能分类
        for i, skill_name in enumerate(config['config'].get('soft_skills', [])):
            existing = SkillCategory.query.filter_by(
                profession_template_id=template.id,
                category_type='soft',
                name=skill_name
            ).first()
            
            if existing:
                continue
            
            skill_category = SkillCategory(
                profession_template_id=template.id,
                category_type='soft',
                name=skill_name,
                display_name=skill_name,
                description=f'{skill_name}相关能力',
                sort_order=i,
                is_active=True
            )
            db.session.add(skill_category)
    
    db.session.commit()
    print("✅ 技能分类创建完成")

def migrate_existing_data():
    """迁移现有数据，添加职业类型标记"""
    print("🔧 迁移现有数据...")
    
    # 迁移用户数据
    users = User.query.all()
    for user in users:
        if not user.profession_type:
            user.profession_type = 'ai_engineer'
            print(f"  👤 用户 {user.username} 设置为 AI工程师")
    
    # 迁移技术技能数据
    tech_skills = TechSkill.query.all()
    for skill in tech_skills:
        if not hasattr(skill, 'profession_type') or not skill.profession_type:
            skill.profession_type = 'ai_engineer'
    
    # 迁移软技能数据
    soft_skills = SoftSkill.query.all()
    for skill in soft_skills:
        if not hasattr(skill, 'profession_type'):
            # 如果模型还没有这个字段，先跳过
            continue
    
    db.session.commit()
    print("✅ 现有数据迁移完成")

def create_sample_data_for_professions():
    """为不同职业创建示例数据"""
    print("🔧 创建多职业示例数据...")
    
    # 为前端开发工程师创建示例数据
    frontend_skills = [
        {'category': 'languages', 'name': 'JavaScript/ES6+', 'score': 4.5, 'evidence': '熟练使用现代JavaScript特性开发复杂应用', 'priority': 'H'},
        {'category': 'languages', 'name': 'TypeScript', 'score': 4.0, 'evidence': '使用TypeScript开发大型项目，提升代码质量', 'priority': 'H'},
        {'category': 'frameworks', 'name': 'React', 'score': 4.5, 'evidence': '深度掌握React生态，开发过多个生产级应用', 'priority': 'H'},
        {'category': 'frameworks', 'name': 'Vue.js', 'score': 3.5, 'evidence': '熟悉Vue.js框架，有实际项目经验', 'priority': 'M'},
        {'category': 'tools', 'name': 'Webpack/Vite', 'score': 3.5, 'evidence': '配置和优化构建工具，提升开发效率', 'priority': 'M'},
    ]
    
    # 检查是否已有前端工程师用户
    frontend_user = User.query.filter_by(profession_type='frontend_developer').first()
    if not frontend_user:
        # 创建示例用户（这里简化处理，实际应该通过注册流程）
        print("  📝 创建前端开发工程师示例技能数据")
        for skill_data in frontend_skills:
            # 这里可以创建示例技能数据
            pass
    
    # 为产品经理创建示例数据
    pm_skills = [
        {'category': 'analysis', 'name': '数据分析', 'score': 4.0, 'evidence': '熟练使用SQL和Excel进行数据分析', 'priority': 'H'},
        {'category': 'design', 'name': '原型设计', 'score': 4.5, 'evidence': '使用Figma和Axure制作高保真原型', 'priority': 'H'},
        {'category': 'research', 'name': '用户调研', 'score': 3.5, 'evidence': '设计和执行用户访谈、问卷调研', 'priority': 'M'},
    ]
    
    print("✅ 多职业示例数据创建完成")

def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    # 检查职业模板
    templates = ProfessionTemplate.query.all()
    print(f"  📋 职业模板数量: {len(templates)}")
    for template in templates:
        print(f"    - {template.display_name} ({template.name})")
    
    # 检查技能分类
    categories = SkillCategory.query.all()
    print(f"  📂 技能分类数量: {len(categories)}")
    
    # 检查用户职业类型
    users = User.query.all()
    profession_counts = {}
    for user in users:
        profession = user.profession_type or 'unknown'
        profession_counts[profession] = profession_counts.get(profession, 0) + 1
    
    print(f"  👥 用户职业分布:")
    for profession, count in profession_counts.items():
        template = ProfessionTemplate.query.filter_by(name=profession).first()
        display_name = template.display_name if template else profession
        print(f"    - {display_name}: {count}人")
    
    # 检查技能数据
    tech_skills = TechSkill.query.all()
    soft_skills = SoftSkill.query.all()
    print(f"  🎯 技术技能数量: {len(tech_skills)}")
    print(f"  🤝 软技能数量: {len(soft_skills)}")
    
    print("✅ 迁移验证完成")

def main():
    """主迁移流程"""
    print("🚀 开始通用化迁移...")
    print("=" * 50)
    
    with app.app_context():
        try:
            # 创建新表（如果不存在）
            db.create_all()
            
            # 1. 创建职业模板
            create_profession_templates()
            
            # 2. 创建技能分类
            create_skill_categories()
            
            # 3. 迁移现有数据
            migrate_existing_data()
            
            # 4. 创建多职业示例数据
            create_sample_data_for_professions()
            
            # 5. 验证迁移结果
            verify_migration()
            
            print("=" * 50)
            print("🎉 通用化迁移完成！")
            print("\n📋 迁移总结:")
            print("✅ 职业模板系统已建立")
            print("✅ 技能分类配置已创建")
            print("✅ 现有数据已标记职业类型")
            print("✅ 多职业示例数据已准备")
            print("\n🔄 下一步:")
            print("1. 更新前端界面以支持职业选择")
            print("2. 测试多职业功能")
            print("3. 创建用户迁移指南")
            print("4. 部署到生产环境")
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    main()
