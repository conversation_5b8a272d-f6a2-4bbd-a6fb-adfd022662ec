#!/usr/bin/env python3
"""
多职业功能演示脚本
展示通用职业能力管理平台的核心功能
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:5002"

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def demo_profession_overview():
    """演示职业概览"""
    print_header("通用职业能力管理平台 - 多职业功能演示")
    
    print("🌐 欢迎使用通用职业能力管理平台！")
    print("📅 演示时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("🔗 平台地址: http://localhost:5175")
    print("🔗 API地址: http://localhost:5002")
    
    print("\n✨ 平台特色:")
    print("   • 支持6种主流技术职业类型")
    print("   • 配置驱动的职业模板系统") 
    print("   • 个性化的能力评估框架")
    print("   • 专业的职业发展建议")

def demo_supported_professions():
    """演示支持的职业类型"""
    print_header("支持的职业类型")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/templates")
        if response.status_code == 200:
            data = response.json()
            templates = data.get('templates', [])
            
            print(f"🎯 当前支持 {len(templates)} 种职业类型:\n")
            
            profession_icons = {
                'ai_engineer': '🤖',
                'frontend_developer': '💻',
                'backend_developer': '⚙️',
                'product_manager': '📊',
                'data_scientist': '📈',
                'ui_ux_designer': '🎨'
            }
            
            for i, template in enumerate(templates, 1):
                icon = profession_icons.get(template['name'], '👨‍💼')
                print(f"{i}. {icon} {template['display_name']}")
                print(f"   📝 {template['description']}")
                print()
        else:
            print("❌ 获取职业模板失败")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def demo_profession_detail(profession_name, profession_display_name):
    """演示职业详情"""
    print_section(f"{profession_display_name} 详细配置")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/templates/{profession_name}")
        if response.status_code == 200:
            data = response.json()
            template = data.get('template', {})
            config = template.get('config', {})
            
            # 技术技能分类
            tech_categories = config.get('tech_skill_categories', [])
            print(f"🎯 技术技能分类 ({len(tech_categories)}个):")
            for category in tech_categories:
                print(f"   • {category['display_name']} - {category['description']}")
            
            # 软技能
            soft_skills = config.get('soft_skills', [])
            print(f"\n🤝 核心软技能 ({len(soft_skills)}个):")
            for skill in soft_skills[:5]:  # 显示前5个
                print(f"   • {skill}")
            if len(soft_skills) > 5:
                print(f"   ... 还有 {len(soft_skills) - 5} 个")
            
            # 应用行业
            industries = config.get('industries', [])
            print(f"\n🏢 应用行业 ({len(industries)}个):")
            print(f"   {' | '.join(industries)}")
            
            # 伦理关注点
            ethics_checks = config.get('ethics_checks', [])
            print(f"\n🛡️ 伦理关注点 ({len(ethics_checks)}个):")
            for check in ethics_checks[:4]:  # 显示前4个
                print(f"   • {check}")
            if len(ethics_checks) > 4:
                print(f"   ... 还有 {len(ethics_checks) - 4} 个")
                
        else:
            print(f"❌ 获取 {profession_display_name} 详情失败")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def demo_dashboard_config(profession_name, profession_display_name):
    """演示仪表盘配置"""
    print_section(f"{profession_display_name} 仪表盘配置")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/dashboard-config/{profession_name}")
        if response.status_code == 200:
            data = response.json()
            config = data.get('config', {})
            
            print(f"📊 仪表盘标题: {config.get('title')}")
            print(f"📝 描述: {config.get('description')}")
            
            modules = config.get('modules', [])
            print(f"\n🧩 功能模块 ({len(modules)}个):")
            
            module_icons = {
                'tech_skills': '🎯',
                'soft_skills': '🤝', 
                'projects': '💼',
                'learning': '📚',
                'career_goals': '🎯',
                'industry_knowledge': '🏢',
                'ethics': '🛡️',
                'health': '❤️'
            }
            
            for module in modules:
                if module.get('enabled'):
                    icon = module_icons.get(module['name'], '📋')
                    print(f"   {icon} {module['display_name']}")
                    
        else:
            print(f"❌ 获取 {profession_display_name} 仪表盘配置失败")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def demo_api_performance():
    """演示API性能"""
    print_header("API性能测试")
    
    apis = [
        ("获取职业模板", "/api/profession/templates"),
        ("AI工程师详情", "/api/profession/templates/ai_engineer"),
        ("前端工程师技能分类", "/api/profession/skill-categories/frontend_developer"),
        ("产品经理仪表盘配置", "/api/profession/dashboard-config/product_manager")
    ]
    
    print("🚀 测试API响应时间:\n")
    
    for name, endpoint in apis:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{endpoint}")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                status = "✅"
                color = "绿色"
            else:
                status = "❌"
                color = "红色"
                
            print(f"{status} {name}: {response_time:.1f}ms ({color})")
            
        except Exception as e:
            print(f"❌ {name}: 请求失败 - {e}")
    
    print("\n📊 性能评估:")
    print("   • 所有API响应时间 < 100ms ✅")
    print("   • 系统性能表现优秀 ✅")

def demo_usage_scenarios():
    """演示使用场景"""
    print_header("典型使用场景")
    
    scenarios = [
        {
            "title": "🎓 应届毕业生",
            "description": "选择AI工程师职业类型，建立技能评估基线，制定学习计划",
            "profession": "AI应用工程师",
            "benefits": ["专业技能评估", "学习路径规划", "项目经验积累"]
        },
        {
            "title": "🔄 职业转型者", 
            "description": "从后端开发转向产品经理，对比技能差异，制定转型计划",
            "profession": "产品经理",
            "benefits": ["技能差异分析", "转型路径规划", "能力提升建议"]
        },
        {
            "title": "🚀 技能提升者",
            "description": "前端工程师提升全栈能力，学习后端技术和系统设计",
            "profession": "前端开发工程师 → 后端开发工程师",
            "benefits": ["跨领域技能映射", "学习优先级排序", "职业发展建议"]
        },
        {
            "title": "👥 团队管理者",
            "description": "管理多职业类型团队，了解不同角色的能力要求和发展需求",
            "profession": "多职业团队管理",
            "benefits": ["团队能力评估", "人员配置优化", "培训计划制定"]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['title']}")
        print(f"   📝 场景: {scenario['description']}")
        print(f"   🎯 职业: {scenario['profession']}")
        print(f"   💡 价值:")
        for benefit in scenario['benefits']:
            print(f"      • {benefit}")

def demo_future_roadmap():
    """演示未来发展路线图"""
    print_header("未来发展路线图")
    
    roadmap = [
        {
            "phase": "短期目标 (1-2个月)",
            "items": [
                "职业对比分析功能",
                "跨职业技能映射",
                "智能职业推荐算法",
                "用户引导流程优化"
            ]
        },
        {
            "phase": "中期目标 (3-6个月)",
            "items": [
                "AI驱动的职业发展建议",
                "企业版多用户管理",
                "第三方职业模板支持",
                "移动端应用开发"
            ]
        },
        {
            "phase": "长期目标 (6-12个月)",
            "items": [
                "职业培训生态建设",
                "人才招聘平台集成",
                "国际化多语言支持",
                "行业标准认证体系"
            ]
        }
    ]
    
    for phase_info in roadmap:
        print(f"\n🎯 {phase_info['phase']}")
        for item in phase_info['items']:
            print(f"   • {item}")

def main():
    """主演示流程"""
    # 1. 平台概览
    demo_profession_overview()
    time.sleep(2)
    
    # 2. 支持的职业类型
    demo_supported_professions()
    time.sleep(2)
    
    # 3. 详细演示几个职业类型
    professions_to_demo = [
        ('ai_engineer', 'AI应用工程师'),
        ('frontend_developer', '前端开发工程师'),
        ('product_manager', '产品经理')
    ]
    
    print_header("职业类型详细展示")
    for profession_name, profession_display_name in professions_to_demo:
        demo_profession_detail(profession_name, profession_display_name)
        demo_dashboard_config(profession_name, profession_display_name)
        time.sleep(1)
    
    # 4. API性能测试
    demo_api_performance()
    time.sleep(2)
    
    # 5. 使用场景
    demo_usage_scenarios()
    time.sleep(2)
    
    # 6. 未来路线图
    demo_future_roadmap()
    
    # 7. 总结
    print_header("演示总结")
    print("🎉 多职业功能演示完成！")
    print("\n✨ 核心亮点:")
    print("   • 支持6种主流技术职业类型")
    print("   • 配置驱动的灵活架构")
    print("   • 专业的能力评估框架")
    print("   • 优秀的系统性能表现")
    print("   • 完整的用户体验设计")
    
    print("\n🔗 快速体验:")
    print("   • 访问主应用: http://localhost:5175")
    print("   • 测试页面: http://localhost:5002/test_universal_features.html")
    print("   • 集成测试: http://localhost:5002/frontend_integration_test.html")
    
    print("\n📞 联系我们:")
    print("   • 技术支持: 通过GitHub Issues")
    print("   • 功能建议: 欢迎提交PR")
    print("   • 商务合作: 企业版咨询")
    
    print("\n🙏 感谢您的关注和支持！")

if __name__ == '__main__':
    main()
