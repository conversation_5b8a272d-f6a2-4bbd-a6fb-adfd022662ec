# 创建新的目录结构
mkdir -p .github/ISSUE_TEMPLATE
mkdir -p .github/workflows
mkdir -p docs/getting-started
mkdir -p docs/user-guide/professions
mkdir -p docs/user-guide/features
mkdir -p docs/developer-guide
mkdir -p docs/deployment
mkdir -p assets
mkdir -p scripts
mkdir -p examples
mkdir -p tests

# 移动现有文件到新结构
mv main.py backend/
mv requirements.txt backend/
mv *.py backend/ 2>/dev/null
mv instance/ backend/

# 创建必要的空文件
touch .github/PULL_REQUEST_TEMPLATE.md
touch .github/ISSUE_TEMPLATE/bug_report.md
touch .github/ISSUE_TEMPLATE/feature_request.md
touch .github/workflows/ci.yml
touch CONTRIBUTING.md
touch CODE_OF_CONDUCT.md
touch CHANGELOG.md
touch SECURITY.md
touch docs/README.md