#!/usr/bin/env python3
"""
AI应用工程师虚拟数据初始化脚本
为8大能力模块创建示例数据
"""

import sys
import os
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import app
from assessment import db, TechSkill, SoftSkill, Project, LearningActivity, CareerGoal, IndustryKnowledge, EthicsCheck, HealthMetric

def init_sample_data():
    """初始化AI应用工程师的示例数据"""

    with app.app_context():
        # 清空现有数据
        db.drop_all()
        db.create_all()

        # 1. 技术能力数据
        tech_skills_data = [
            # 编程语言
            {'category': '编程语言', 'name': 'Python高级特性', 'score': 4.5, 'evidence': '熟练使用装饰器、生成器、上下文管理器等高级特性，开发了多个AI应用项目', 'priority': 'H'},
            {'category': '编程语言', 'name': 'JavaScript/TypeScript', 'score': 4.0, 'evidence': '使用React和Node.js开发了AI应用的前后端，熟练掌握异步编程', 'priority': 'H'},
            {'category': '编程语言', 'name': 'SQL', 'score': 3.5, 'evidence': '能够编写复杂查询，优化数据库性能，设计合理的数据库结构', 'priority': 'M'},

            # 框架工具
            {'category': '框架工具', 'name': 'TensorFlow/Keras', 'score': 4.0, 'evidence': '使用TensorFlow开发了图像分类和NLP模型，熟悉模型训练和优化', 'priority': 'H'},
            {'category': '框架工具', 'name': 'PyTorch', 'score': 3.5, 'evidence': '使用PyTorch实现了Transformer模型，进行了模型微调和部署', 'priority': 'H'},
            {'category': '框架工具', 'name': 'LangChain', 'score': 4.5, 'evidence': '深度掌握LangChain框架，开发了多个智能体应用和RAG系统', 'priority': 'H'},
            {'category': '框架工具', 'name': 'FastAPI', 'score': 4.0, 'evidence': '使用FastAPI构建了高性能的AI API服务，支持异步处理', 'priority': 'M'},

            # 数据处理
            {'category': '数据处理', 'name': 'Pandas/NumPy', 'score': 4.5, 'evidence': '熟练进行数据清洗、特征工程和数据分析，处理过TB级数据', 'priority': 'H'},
            {'category': '数据处理', 'name': 'Elasticsearch', 'score': 3.0, 'evidence': '使用ES构建了向量搜索系统，支持语义检索功能', 'priority': 'M'},
            {'category': '数据处理', 'name': 'Redis', 'score': 3.5, 'evidence': '使用Redis进行缓存优化和会话管理，提升系统性能', 'priority': 'M'},

            # 模型能力
            {'category': '模型能力', 'name': '大语言模型应用', 'score': 4.5, 'evidence': '深度使用GPT、Claude等模型，开发了智能客服和代码生成工具', 'priority': 'H'},
            {'category': '模型能力', 'name': '计算机视觉', 'score': 3.5, 'evidence': '实现了图像识别和目标检测系统，应用于工业质检场景', 'priority': 'M'},
            {'category': '模型能力', 'name': 'RAG系统设计', 'score': 4.0, 'evidence': '设计并实现了企业级RAG系统，支持多模态检索', 'priority': 'H'},

            # 工程化
            {'category': '工程化', 'name': 'Docker/Kubernetes', 'score': 3.5, 'evidence': '使用容器化技术部署AI应用，实现了自动扩缩容', 'priority': 'M'},
            {'category': '工程化', 'name': 'CI/CD', 'score': 3.0, 'evidence': '建立了完整的CI/CD流水线，实现了模型的自动化部署', 'priority': 'M'},
            {'category': '工程化', 'name': 'AWS/云服务', 'score': 3.5, 'evidence': '使用AWS部署了大规模AI应用，熟悉云原生架构', 'priority': 'M'},
        ]

        for skill_data in tech_skills_data:
            skill = TechSkill(user_id=1, **skill_data)
            db.session.add(skill)

        # 2. 软技能数据
        soft_skills_data = [
            {'name': '沟通表达', 'score': 4, 'evidence': '能够清晰地向非技术人员解释AI技术原理，多次进行技术分享'},
            {'name': '团队协作', 'score': 4.5, 'evidence': '在跨职能团队中协作开发AI产品，善于协调技术和业务需求'},
            {'name': '问题解决', 'score': 4.5, 'evidence': '善于分析复杂技术问题，提出创新解决方案'},
            {'name': '学习能力', 'score': 5, 'evidence': '快速掌握新技术，持续跟进AI领域最新发展'},
            {'name': '项目管理', 'score': 3.5, 'evidence': '负责过多个AI项目的技术管理，能够合理安排开发进度'},
            {'name': '创新思维', 'score': 4, 'evidence': '提出了多个创新的AI应用方案，获得了技术专利'},
            {'name': '压力应对', 'score': 3.5, 'evidence': '在高压环境下保持冷静，能够在紧急情况下快速响应'},
            {'name': '批判性思维', 'score': 4, 'evidence': '能够客观评估AI技术的优缺点，做出理性的技术决策'},
        ]

        for skill_data in soft_skills_data:
            skill = SoftSkill(user_id=1, **skill_data)
            db.session.add(skill)

        # 3. 项目经验数据
        projects_data = [
            {
                'title': '智能客服系统',
                'objective': '构建基于大语言模型的智能客服系统，提升客户服务效率',
                'tech_challenge': '如何处理多轮对话上下文，确保回答的准确性和一致性',
                'solution': '使用LangChain构建对话管理系统，结合RAG技术提供准确的知识检索',
                'outcome': '客户满意度提升30%，人工客服工作量减少60%',
                'reflection': '需要更好的意图识别和情感分析能力',
                'start_date': datetime(2023, 8, 1).date(),
                'end_date': datetime(2023, 11, 30).date()
            },
            {
                'title': '代码生成助手',
                'objective': '开发AI代码生成工具，提升开发效率',
                'tech_challenge': '如何生成高质量、符合规范的代码',
                'solution': '微调Code Llama模型，结合静态代码分析工具',
                'outcome': '开发效率提升40%，代码质量显著改善',
                'reflection': '需要加强对复杂业务逻辑的理解',
                'start_date': datetime(2023, 12, 1).date(),
                'end_date': datetime(2024, 3, 15).date()
            },
            {
                'title': '文档智能分析平台',
                'objective': '构建企业文档智能分析和检索系统',
                'tech_challenge': '处理多种格式文档，实现精确的语义检索',
                'solution': '使用多模态模型处理文档，构建向量数据库',
                'outcome': '文档检索准确率达到95%，查询速度提升10倍',
                'reflection': '需要优化长文档的处理能力',
                'start_date': datetime(2024, 4, 1).date(),
                'end_date': datetime(2024, 7, 30).date()
            }
        ]

        for project_data in projects_data:
            project = Project(user_id=1, **project_data)
            db.session.add(project)

        # 4. 学习活动数据
        learning_activities_data = [
            {'activity_type': '课程', 'title': 'LangChain深度实战', 'hours_spent': 40, 'completion_status': '已完成', 'notes': '深入学习了LangChain的高级特性', 'date': datetime(2024, 1, 15).date()},
            {'activity_type': '书籍', 'title': '深度学习实战', 'hours_spent': 60, 'completion_status': '已完成', 'notes': '系统学习了深度学习理论和实践', 'date': datetime(2024, 2, 20).date()},
            {'activity_type': '论文', 'title': 'Attention Is All You Need', 'hours_spent': 8, 'completion_status': '已完成', 'notes': '深入理解Transformer架构', 'date': datetime(2024, 3, 10).date()},
            {'activity_type': '视频', 'title': 'GPT-4技术解析', 'hours_spent': 12, 'completion_status': '已完成', 'notes': '了解了GPT-4的技术细节', 'date': datetime(2024, 3, 25).date()},
            {'activity_type': '实践项目', 'title': 'RAG系统实现', 'hours_spent': 80, 'completion_status': '进行中', 'notes': '正在实现企业级RAG系统', 'date': datetime(2024, 4, 1).date()},
            {'activity_type': '会议/讲座', 'title': 'AI技术峰会', 'hours_spent': 16, 'completion_status': '已完成', 'notes': '了解了AI行业最新趋势', 'date': datetime(2024, 4, 15).date()},
        ]

        for activity_data in learning_activities_data:
            activity = LearningActivity(user_id=1, **activity_data)
            db.session.add(activity)

        # 5. 职业发展目标
        career_goals_data = [
            {'title': '成为AI架构师', 'goal_type': '长期目标', 'description': '在2年内成长为资深AI架构师', 'progress': 60, 'resource_gap': '需要更多大型项目经验和架构设计能力', 'target_date': datetime(2025, 12, 31).date()},
            {'title': '掌握多模态AI', 'goal_type': '技能提升', 'description': '深入学习多模态AI技术', 'progress': 40, 'resource_gap': '需要更多实践项目和相关课程', 'target_date': datetime(2024, 8, 31).date()},
            {'title': '发表技术论文', 'goal_type': '短期目标', 'description': '在顶级会议发表AI相关论文', 'progress': 30, 'resource_gap': '需要更深入的研究和学术合作', 'target_date': datetime(2024, 12, 31).date()},
            {'title': '建立技术团队', 'goal_type': '职位晋升', 'description': '组建并领导AI技术团队', 'progress': 50, 'resource_gap': '需要提升管理和领导能力', 'target_date': datetime(2025, 6, 30).date()},
        ]

        for goal_data in career_goals_data:
            goal = CareerGoal(user_id=1, **goal_data)
            db.session.add(goal)

        # 6. 行业知识匹配度数据
        industry_knowledge_data = [
            {'industry': '金融科技', 'dimension': '领域知识', 'question': '我是否了解金融科技行业的主要监管要求？', 'status': '熟悉PCI DSS、SOX等合规要求，了解金融数据安全标准'},
            {'industry': '金融科技', 'dimension': '业务场景', 'question': '我能否设计风控AI系统？', 'status': '具备反欺诈模型开发经验，了解信贷风险评估'},
            {'industry': '医疗健康', 'dimension': '合规要求', 'question': '我是否了解医疗AI的合规标准？', 'status': '了解HIPAA、FDA医疗器械认证等基本要求'},
            {'industry': '医疗健康', 'dimension': '技术标准', 'question': '我能否处理医疗影像数据？', 'status': '有医疗影像分析项目经验，熟悉DICOM标准'},
            {'industry': '教育科技', 'dimension': '业务场景', 'question': '我能否设计个性化学习系统？', 'status': '了解推荐算法，有在线教育平台开发经验'},
            {'industry': '智能制造', 'dimension': '技术标准', 'question': '我是否了解工业4.0相关技术？', 'status': '了解IoT、边缘计算，有工业质检AI项目经验'},
        ]

        for knowledge_data in industry_knowledge_data:
            knowledge = IndustryKnowledge(user_id=1, **knowledge_data)
            db.session.add(knowledge)

        # 7. 伦理与合规自检数据
        ethics_checks_data = [
            {'check_item': '数据隐私保护：是否确保用户数据的安全和隐私？', 'status': True, 'practice_case': '在所有项目中实施数据脱敏和加密，严格控制数据访问权限'},
            {'check_item': '算法公平性：AI模型是否避免了偏见和歧视？', 'status': True, 'practice_case': '建立了模型公平性评估流程，定期检查不同群体的预测结果'},
            {'check_item': '透明度原则：是否向用户说明AI系统的工作原理？', 'status': True, 'practice_case': '为用户提供AI决策的解释性报告，使用可解释AI技术'},
            {'check_item': '责任归属：是否明确AI系统决策的责任主体？', 'status': True, 'practice_case': '建立了AI决策的审计机制，明确人工审核流程'},
            {'check_item': '安全可靠：AI系统是否经过充分测试和验证？', 'status': True, 'practice_case': '建立了完整的测试框架，包括对抗性测试和压力测试'},
            {'check_item': '用户同意：是否获得用户对数据使用的明确同意？', 'status': True, 'practice_case': '实施了明确的用户同意机制，提供数据使用说明'},
            {'check_item': '可解释性：AI决策过程是否可以被理解和解释？', 'status': False, 'practice_case': '正在研究和实施更好的可解释性技术'},
            {'check_item': '持续监控：是否建立了AI系统的持续监控机制？', 'status': True, 'practice_case': '建立了模型性能监控系统，定期评估模型表现'},
        ]

        for check_data in ethics_checks_data:
            check = EthicsCheck(user_id=1, **check_data)
            db.session.add(check)

        # 8. 健康与动力评估数据
        base_date = datetime.now() - timedelta(days=30)
        health_metrics_data = []

        for i in range(30):
            current_date = base_date + timedelta(days=i)
            # 模拟一些波动的健康数据
            health_metrics_data.extend([
                {'metric_type': '工作节奏', 'value': random.randint(6, 9), 'description': '今日工作节奏适中', 'date': current_date.date()},
                {'metric_type': '学习倦怠', 'value': random.randint(2, 5), 'description': '学习状态良好', 'date': current_date.date()},
                {'metric_type': '社交充电', 'value': random.randint(5, 8), 'description': '团队协作顺畅', 'date': current_date.date()},
                {'metric_type': '身体信号', 'value': random.randint(6, 9), 'description': '身体状态良好', 'date': current_date.date()},
                {'metric_type': '动力水平', 'value': random.randint(7, 10), 'description': '工作动力充足', 'date': current_date.date()},
                {'metric_type': '压力指数', 'value': random.randint(3, 6), 'description': '压力在可控范围内', 'date': current_date.date()},
            ])

        for metric_data in health_metrics_data:
            metric = HealthMetric(user_id=1, **metric_data)
            db.session.add(metric)

        db.session.commit()
        print("✅ AI应用工程师示例数据初始化完成！")
        print(f"📊 技术技能: {len(tech_skills_data)} 项")
        print(f"🤝 软技能: {len(soft_skills_data)} 项")
        print(f"🚀 项目经验: {len(projects_data)} 个")
        print(f"📚 学习活动: {len(learning_activities_data)} 项")
        print(f"🎯 职业目标: {len(career_goals_data)} 个")
        print(f"🏭 行业知识: {len(industry_knowledge_data)} 项")
        print(f"🛡️ 伦理检查: {len(ethics_checks_data)} 项")
        print(f"❤️ 健康指标: {len(health_metrics_data)} 条记录")
        print("\n🎉 8大能力模块数据全部初始化完成！")

if __name__ == '__main__':
    init_sample_data()
