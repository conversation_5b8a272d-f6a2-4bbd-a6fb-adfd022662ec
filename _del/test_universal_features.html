<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用职业能力管理平台 - 功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .profession-card {
            transition: all 0.3s ease;
        }
        .profession-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .selected {
            border-color: #3B82F6;
            background-color: #EFF6FF;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">🌐 通用职业能力管理平台</h1>
            <p class="text-lg text-gray-600">支持多种职业类型的能力评估和发展管理</p>
            <div class="mt-4 inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                系统已成功通用化改造
            </div>
        </div>

        <!-- 功能测试区域 -->
        <div class="max-w-6xl mx-auto">
            <!-- 职业模板展示 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                    <span class="text-2xl mr-2">👥</span>
                    支持的职业类型
                </h2>
                <div id="professions-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 职业卡片将通过JavaScript动态加载 -->
                </div>
            </div>

            <!-- 职业详情展示 -->
            <div id="profession-details" class="bg-white rounded-lg shadow-lg p-6 mb-8" style="display: none;">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                    <span class="text-2xl mr-2">📋</span>
                    职业详细信息
                </h2>
                <div id="profession-content">
                    <!-- 职业详情内容 -->
                </div>
            </div>

            <!-- API测试区域 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                    <span class="text-2xl mr-2">🔧</span>
                    API功能测试
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">测试按钮</h3>
                        <div class="space-y-3">
                            <button onclick="testGetTemplates()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                获取所有职业模板
                            </button>
                            <button onclick="testGetSkillCategories()" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                获取技能分类
                            </button>
                            <button onclick="testDashboardConfig()" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                获取仪表盘配置
                            </button>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">测试结果</h3>
                        <div id="test-results" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto">
                            <p class="text-gray-500">点击测试按钮查看API响应结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedProfession = 'ai_engineer';
        let professions = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProfessions();
        });

        // 加载所有职业模板
        async function loadProfessions() {
            try {
                const response = await fetch('/api/profession/templates');
                const data = await response.json();
                professions = data.templates;
                renderProfessions();
            } catch (error) {
                console.error('加载职业模板失败:', error);
                addTestResult('❌ 加载职业模板失败: ' + error.message, 'error');
            }
        }

        // 渲染职业卡片
        function renderProfessions() {
            const grid = document.getElementById('professions-grid');
            grid.innerHTML = '';

            professions.forEach(profession => {
                const card = document.createElement('div');
                card.className = `profession-card bg-white border-2 border-gray-200 rounded-lg p-6 cursor-pointer ${
                    profession.name === selectedProfession ? 'selected' : ''
                }`;
                card.onclick = () => selectProfession(profession.name);

                const icon = getProfessionIcon(profession.name);
                card.innerHTML = `
                    <div class="text-center">
                        <div class="text-4xl mb-3">${icon}</div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">${profession.display_name}</h3>
                        <p class="text-sm text-gray-600">${profession.description}</p>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        // 获取职业图标
        function getProfessionIcon(professionName) {
            const iconMap = {
                'ai_engineer': '🤖',
                'frontend_developer': '💻',
                'backend_developer': '⚙️',
                'product_manager': '📊',
                'data_scientist': '📈',
                'ui_ux_designer': '🎨'
            };
            return iconMap[professionName] || '👨‍💼';
        }

        // 选择职业
        async function selectProfession(professionName) {
            selectedProfession = professionName;
            renderProfessions();
            await loadProfessionDetails(professionName);
        }

        // 加载职业详情
        async function loadProfessionDetails(professionName) {
            try {
                const response = await fetch(`/api/profession/templates/${professionName}`);
                const data = await response.json();
                renderProfessionDetails(data.template);
            } catch (error) {
                console.error('加载职业详情失败:', error);
                addTestResult('❌ 加载职业详情失败: ' + error.message, 'error');
            }
        }

        // 渲染职业详情
        function renderProfessionDetails(template) {
            const detailsDiv = document.getElementById('profession-details');
            const contentDiv = document.getElementById('profession-content');
            
            const config = template.config;
            contentDiv.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <span class="text-blue-600 mr-2">🎯</span>
                            技术技能分类
                        </h3>
                        <div class="space-y-2">
                            ${config.tech_skill_categories.map(cat => 
                                `<div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                                    <span class="text-sm">${cat.display_name}</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <span class="text-green-600 mr-2">🤝</span>
                            软技能要求
                        </h3>
                        <div class="space-y-2">
                            ${config.soft_skills.map(skill => 
                                `<div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-600 rounded-full"></div>
                                    <span class="text-sm">${skill}</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <span class="text-purple-600 mr-2">🏢</span>
                            应用行业
                        </h3>
                        <div class="flex flex-wrap gap-2">
                            ${config.industries.map(industry => 
                                `<span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">${industry}</span>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <span class="text-orange-600 mr-2">🛡️</span>
                            伦理关注点
                        </h3>
                        <div class="space-y-2">
                            ${config.ethics_checks.slice(0, 4).map(check => 
                                `<div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-orange-600 rounded-full"></div>
                                    <span class="text-sm">${check}</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            detailsDiv.style.display = 'block';
        }

        // 测试API功能
        async function testGetTemplates() {
            try {
                const response = await fetch('/api/profession/templates');
                const data = await response.json();
                addTestResult('✅ 获取职业模板成功: ' + JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addTestResult('❌ 获取职业模板失败: ' + error.message, 'error');
            }
        }

        async function testGetSkillCategories() {
            try {
                const response = await fetch(`/api/profession/skill-categories/${selectedProfession}`);
                const data = await response.json();
                addTestResult('✅ 获取技能分类成功: ' + JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addTestResult('❌ 获取技能分类失败: ' + error.message, 'error');
            }
        }

        async function testDashboardConfig() {
            try {
                const response = await fetch(`/api/profession/dashboard-config/${selectedProfession}`);
                const data = await response.json();
                addTestResult('✅ 获取仪表盘配置成功: ' + JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addTestResult('❌ 获取仪表盘配置失败: ' + error.message, 'error');
            }
        }

        // 添加测试结果
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const resultElement = document.createElement('div');
            resultElement.className = `mb-2 p-2 rounded ${
                type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`;
            resultElement.innerHTML = `<pre class="text-xs whitespace-pre-wrap">${message}</pre>`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
