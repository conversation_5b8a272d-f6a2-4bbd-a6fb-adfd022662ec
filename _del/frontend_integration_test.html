<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端集成测试 - 多职业功能</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-item {
            transition: all 0.3s ease;
        }
        .test-success {
            background-color: #f0f9ff;
            border-color: #0ea5e9;
        }
        .test-error {
            background-color: #fef2f2;
            border-color: #ef4444;
        }
        .test-pending {
            background-color: #fffbeb;
            border-color: #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">🧪 前端集成测试</h1>
            <p class="text-lg text-gray-600">多职业功能前端集成验证</p>
            <div class="mt-4 inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
                正在测试前端集成功能
            </div>
        </div>

        <!-- 测试控制面板 -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">测试控制面板</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="runAllTests()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        🚀 运行所有测试
                    </button>
                    <button onclick="testProfessionAPI()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        🔧 测试职业API
                    </button>
                    <button onclick="testComponentIntegration()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        🧩 测试组件集成
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试结果展示 -->
        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">测试结果</h2>
                
                <!-- 测试统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="text-2xl font-bold text-blue-600" id="total-tests">0</div>
                        <div class="text-sm text-blue-600">总测试数</div>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="text-2xl font-bold text-green-600" id="passed-tests">0</div>
                        <div class="text-sm text-green-600">通过测试</div>
                    </div>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="text-2xl font-bold text-red-600" id="failed-tests">0</div>
                        <div class="text-sm text-red-600">失败测试</div>
                    </div>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="text-2xl font-bold text-yellow-600" id="success-rate">0%</div>
                        <div class="text-sm text-yellow-600">成功率</div>
                    </div>
                </div>

                <!-- 测试项目列表 -->
                <div id="test-results" class="space-y-4">
                    <!-- 测试项目将通过JavaScript动态添加 -->
                </div>
            </div>
        </div>

        <!-- 职业选择器测试区域 -->
        <div class="max-w-4xl mx-auto mt-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">职业选择器测试</h2>
                <div id="profession-selector-test">
                    <!-- 职业选择器将在这里渲染 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentTestIndex = 0;

        // 测试配置
        const tests = [
            { name: '获取职业模板列表', func: testGetProfessionTemplates },
            { name: '获取AI工程师详情', func: () => testGetProfessionDetail('ai_engineer') },
            { name: '获取前端工程师详情', func: () => testGetProfessionDetail('frontend_developer') },
            { name: '获取后端工程师详情', func: () => testGetProfessionDetail('backend_developer') },
            { name: '获取产品经理详情', func: () => testGetProfessionDetail('product_manager') },
            { name: '获取数据科学家详情', func: () => testGetProfessionDetail('data_scientist') },
            { name: '获取UI/UX设计师详情', func: () => testGetProfessionDetail('ui_ux_designer') },
            { name: '测试技能分类API', func: testSkillCategoriesAPI },
            { name: '测试仪表盘配置API', func: testDashboardConfigAPI },
            { name: '测试职业选择器组件', func: testProfessionSelectorComponent }
        ];

        // 运行所有测试
        async function runAllTests() {
            testResults = [];
            currentTestIndex = 0;
            updateTestStats();
            
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                const testElement = createTestElement(test.name, 'pending');
                resultsContainer.appendChild(testElement);

                try {
                    const result = await test.func();
                    updateTestElement(testElement, result ? 'success' : 'error', result ? '✅ 通过' : '❌ 失败');
                    testResults.push(result);
                } catch (error) {
                    updateTestElement(testElement, 'error', `❌ 错误: ${error.message}`);
                    testResults.push(false);
                }

                updateTestStats();
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟以便观察
            }
        }

        // 创建测试元素
        function createTestElement(name, status) {
            const div = document.createElement('div');
            div.className = `test-item border-2 rounded-lg p-4 test-${status}`;
            div.innerHTML = `
                <div class="flex items-center justify-between">
                    <span class="font-medium">${name}</span>
                    <span class="test-status">⏳ 等待中...</span>
                </div>
                <div class="test-details mt-2 text-sm text-gray-600" style="display: none;"></div>
            `;
            return div;
        }

        // 更新测试元素
        function updateTestElement(element, status, statusText, details = '') {
            element.className = `test-item border-2 rounded-lg p-4 test-${status}`;
            element.querySelector('.test-status').textContent = statusText;
            
            if (details) {
                const detailsElement = element.querySelector('.test-details');
                detailsElement.textContent = details;
                detailsElement.style.display = 'block';
            }
        }

        // 更新测试统计
        function updateTestStats() {
            const total = testResults.length;
            const passed = testResults.filter(r => r).length;
            const failed = total - passed;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

            document.getElementById('total-tests').textContent = tests.length;
            document.getElementById('passed-tests').textContent = passed;
            document.getElementById('failed-tests').textContent = failed;
            document.getElementById('success-rate').textContent = `${successRate}%`;
        }

        // 测试函数
        async function testGetProfessionTemplates() {
            const response = await fetch('/api/profession/templates');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            return data.templates && data.templates.length === 6;
        }

        async function testGetProfessionDetail(professionName) {
            const response = await fetch(`/api/profession/templates/${professionName}`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            return data.template && data.template.name === professionName;
        }

        async function testSkillCategoriesAPI() {
            const response = await fetch('/api/profession/skill-categories/ai_engineer');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            return data.tech_skill_categories && data.soft_skills;
        }

        async function testDashboardConfigAPI() {
            const response = await fetch('/api/profession/dashboard-config/frontend_developer');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            return data.config && data.config.title;
        }

        async function testProfessionSelectorComponent() {
            // 模拟职业选择器组件测试
            const testContainer = document.getElementById('profession-selector-test');
            
            try {
                const response = await fetch('/api/profession/templates');
                const data = await response.json();
                const templates = data.templates;

                // 创建简化的职业选择器
                testContainer.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        ${templates.map(template => `
                            <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 cursor-pointer transition-colors">
                                <h3 class="font-semibold text-gray-900">${template.display_name}</h3>
                                <p class="text-sm text-gray-600 mt-1">${template.description}</p>
                            </div>
                        `).join('')}
                    </div>
                `;
                
                return true;
            } catch (error) {
                testContainer.innerHTML = `<p class="text-red-600">组件测试失败: ${error.message}</p>`;
                return false;
            }
        }

        // 单独测试函数
        async function testProfessionAPI() {
            const tests = [
                testGetProfessionTemplates,
                () => testGetProfessionDetail('ai_engineer'),
                testSkillCategoriesAPI,
                testDashboardConfigAPI
            ];

            let passed = 0;
            for (const test of tests) {
                try {
                    const result = await test();
                    if (result) passed++;
                } catch (error) {
                    console.error('测试失败:', error);
                }
            }

            alert(`职业API测试完成: ${passed}/${tests.length} 通过`);
        }

        async function testComponentIntegration() {
            const result = await testProfessionSelectorComponent();
            alert(`组件集成测试: ${result ? '通过' : '失败'}`);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStats();
            console.log('前端集成测试页面已加载');
        });
    </script>
</body>
</html>
